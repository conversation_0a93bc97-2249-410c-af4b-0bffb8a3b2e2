package com.cleevio.fundedmind.application.module.mentoringmeeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.GetMentoringMeetingInCalendarQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.InitiatorType
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.ModificationType
import com.cleevio.fundedmind.domain.mentoringmeeting.exception.MentoringMeetingNotAccessibleException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetMentoringMeetingInCalendarQueryHandlerTest(
    @Autowired private val underTest: GetMentoringMeetingInCalendarQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get mentoring meeting in calendar - verify mappings`() {
        // Given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getTrader(
            id = 1.toUUID(),
            firstName = "John",
            lastName = "Doe",
            entityModifier = {
                it.changeProfilePicture(
                    fileId = dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "trader-url",
                        compressedFileUrl = "trader-url-comp",
                        blurHash = "trader-hash",
                    ).id,
                )
                it.changeMentoringPhoto(
                    fileId = dataHelper.getImage(
                        type = FileType.TRADER_MENTORING_PHOTO,
                        originalFileUrl = "cover-url",
                        compressedFileUrl = "cover-url-comp",
                        blurHash = "cover-hash",
                    ).id,
                )
            },
        )

        dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Jane",
            lastName = "Smith",
            entityModifier = {
                it.changeProfilePicture(
                    fileId = dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "student-url",
                        compressedFileUrl = "student-url-comp",
                        blurHash = "student-hash",
                    ).id,
                )
            },
        )

        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            name = "Product Name",
            description = "Product Description",
        )

        val mentoring = dataHelper.getMentoring(
            productId = 1.toUUID(),
            studentId = 2.toUUID(),
        )

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = mentoring.id,
            color = Color.BLUE,
            startAt = "2025-01-01T10:00:00Z".toInstant(),
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            meetingUrl = "meeting-url",
        )

        // When
        val result = underTest.handle(
            GetMentoringMeetingInCalendarQuery(
                userId = 1.toUUID(),
                mentoringMeetingId = mentoringMeeting.id,
            ),
        )

        // Then
        result.run {
            mentoringMeetingId shouldBe 1.toUUID()
            name shouldBe "Product Name"
            description shouldBe "Product Description"
            color shouldBe Color.BLUE
            startAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
            meetingUrl shouldBe "meeting-url"
            modificationType shouldBe null
            recordingUrl shouldBe null
            coverPhoto shouldNotBe null
            coverPhoto!!.run {
                imageOriginalUrl shouldBe "cover-url"
                imageCompressedUrl shouldBe "cover-url-comp"
                imageBlurHash shouldBe "cover-hash"
            }
            trader.run {
                id shouldBe 1.toUUID()
                firstName shouldBe "John"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "trader-url"
                    imageCompressedUrl shouldBe "trader-url-comp"
                    imageBlurHash shouldBe "trader-hash"
                }
            }
            student.run {
                id shouldBe 2.toUUID()
                firstName shouldBe "Jane"
                lastName shouldBe "Smith"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "student-url"
                    imageCompressedUrl shouldBe "student-url-comp"
                    imageBlurHash shouldBe "student-hash"
                }
            }
        }
    }

    @Test
    fun `should throw when mentoring meeting is cancelled`() {
        // Given
        val adminId = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN).id

        val mentoring = dataHelper.getMentoring(
            productId = dataHelper.getProduct(traderId = dataHelper.getTrader().id).id,
            studentId = dataHelper.getStudent().id,
        )

        val cancelledMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = mentoring.id,
            color = Color.BLUE,
            startAt = "2025-01-01T10:00:00Z".toInstant(),
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            entityModifier = {
                it.cancel(reason = "Test cancellation", initiatorType = InitiatorType.STUDENT)
            },
        )

        // When/Then
        val result = underTest.handle(
            GetMentoringMeetingInCalendarQuery(
                userId = adminId,
                mentoringMeetingId = cancelledMeeting.id,
            ),
        )

        result.modificationType shouldBe ModificationType.CANCELLATION
    }

    @Test
    fun `should throw when mentoring meeting is rescheduled`() {
        // Given
        val adminId = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN).id

        val mentoring = dataHelper.getMentoring(
            productId = dataHelper.getProduct(traderId = dataHelper.getTrader().id).id,
            studentId = dataHelper.getStudent().id,
        )

        val rescheduledMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = mentoring.id,
            color = Color.BLUE,
            startAt = "2025-01-01T10:00:00Z".toInstant(),
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            entityModifier = { it.reschedule(reason = "Test reschedule", initiatorType = InitiatorType.MENTOR) },
        )

        // When/Then
        val result = underTest.handle(
            GetMentoringMeetingInCalendarQuery(
                userId = adminId,
                mentoringMeetingId = rescheduledMeeting.id,
            ),
        )

        result.modificationType shouldBe ModificationType.RESCHEDULE
    }

    @Test
    fun `should throw when trader tries to access not their mentoring meeting`() {
        // Given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        val otherTrader = dataHelper.getTrader(id = 2.toUUID()).also { otherTrader ->
            dataHelper.getAppUser(id = otherTrader.id, userRole = UserRole.TRADER)
        }

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                productId = dataHelper.getProduct(traderId = trader.id).id,
                studentId = dataHelper.getStudent().id,
            ).id,
        )

        // When/Then
        shouldThrow<MentoringMeetingNotAccessibleException> {
            underTest.handle(
                GetMentoringMeetingInCalendarQuery(
                    userId = otherTrader.id,
                    mentoringMeetingId = mentoringMeeting.id,
                ),
            )
        }
    }

    @Test
    fun `should throw when student tries to access not their mentoring meeting`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID()).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val otherStudent = dataHelper.getStudent(id = 2.toUUID()).also { otherStudent ->
            dataHelper.getAppUser(id = otherStudent.id, userRole = UserRole.STUDENT)
        }

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                productId = dataHelper.getProduct(traderId = dataHelper.getTrader().id).id,
                studentId = student.id,
            ).id,
        )

        // When/Then
        shouldThrow<MentoringMeetingNotAccessibleException> {
            underTest.handle(
                GetMentoringMeetingInCalendarQuery(
                    userId = otherStudent.id,
                    mentoringMeetingId = mentoringMeeting.id,
                ),
            )
        }
    }
}

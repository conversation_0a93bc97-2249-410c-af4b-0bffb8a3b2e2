package com.cleevio.fundedmind.application.module.coursemodule

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.AppButtonWithLinkInput
import com.cleevio.fundedmind.application.module.coursemodule.command.CreateNewCourseModuleCommand
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.course.exception.CourseNotFoundException
import com.cleevio.fundedmind.domain.coursemodule.CourseModuleRepository
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleCannotHaveBothRewardActionsException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class CreateNewCourseModuleCommandHandlerTest(
    @Autowired private val underTest: CreateNewCourseModuleCommandHandler,
    @Autowired private val courseModuleRepository: CourseModuleRepository,
) : IntegrationTest() {
    @Test
    fun `should create new course module with coupon code`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        val result = underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
                rewardCouponCode = "1234",
                rewardButton = null,
            ),
        )

        courseModuleRepository.findByIdOrNull(result.id)!!.run {
            courseId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            title shouldBe "Course Module 1"
            description shouldBe "Description"
            shortDescription shouldBe "Short Description"
            thumbnailPictureDesktopFileId shouldBe null
            thumbnailPictureMobileFileId shouldBe null
            rewardPictureFileId shouldBe null
            rewardDescription shouldBe "Reward Description"
            rewardCouponCode shouldBe "1234"
            rewardButton shouldBe null
            comingSoon shouldBe false
        }
    }

    @Test
    fun `should create new course module with button`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        val result = underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
                rewardCouponCode = null,
                rewardButton = AppButtonWithLinkInput(
                    text = "Button",
                    color = Color.PURPLE,
                    linkUrl = "url",
                ),
            ),
        )

        courseModuleRepository.findByIdOrNull(result.id)!!.run {
            courseId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            title shouldBe "Course Module 1"
            description shouldBe "Description"
            shortDescription shouldBe "Short Description"
            thumbnailPictureDesktopFileId shouldBe null
            thumbnailPictureMobileFileId shouldBe null
            rewardPictureFileId shouldBe null
            rewardDescription shouldBe "Reward Description"
            rewardCouponCode shouldBe null
            rewardButton!!.run {
                text shouldBe "Button"
                color shouldBe Color.PURPLE
                linkUrl shouldBe "url"
            }
            comingSoon shouldBe false
        }
    }

    @Test
    fun `should throw if course does not exist`() {
        shouldThrow<CourseNotFoundException> {
            underTest.handle(
                defaultCommand(courseId = 999.toUUID()),
            )
        }
    }

    @Test
    fun `should throw if course module should have both reward coupon code and reward button`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        shouldThrow<CourseModuleCannotHaveBothRewardActionsException> {
            underTest.handle(
                defaultCommand(
                    courseId = 1.toUUID(),
                    rewardCouponCode = "1234",
                    rewardButton = AppButtonWithLinkInput(
                        text = "Button",
                        color = Color.PURPLE,
                        linkUrl = "url",
                    ),
                ),
            )
        }
    }

    @Test
    fun `should create with next course module listing order`() {
        // given
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
        )
        dataHelper.getCourseModule(
            id = 2.toUUID(),
            courseId = 1.toUUID(),
            listingOrder = 2,
        ) { it.softDelete() }

        // when
        val result = underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
            ),
        )

        // then
        courseModuleRepository.findByIdOrNull(result.id)!!.listingOrder shouldBe 2 // highest non-deleted + 1
    }

    private fun defaultCommand(
        courseId: UUID,
        title: String = "Course Module 1",
        description: String = "Description",
        shortDescription: String = "Short Description",
        rewardDescription: String? = "Reward Description",
        rewardCouponCode: String? = null,
        rewardButton: AppButtonWithLinkInput? = null,
        comingSoon: Boolean = false,
    ) = CreateNewCourseModuleCommand(
        courseId = courseId,
        title = title,
        description = description,
        shortDescription = shortDescription,
        rewardDescription = rewardDescription,
        rewardCouponCode = rewardCouponCode,
        rewardButton = rewardButton,
        comingSoon = comingSoon,
    )
}

package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoring.query.GetTraderMentorInfoQuery
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderNotFoundException
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.TraderMentoring
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetTraderMentorInfoQueryHandlerTest(
    @Autowired private val underTest: GetTraderMentorInfoQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get trader info with mentoring - verify mappings`() {
        // given
        val trader = dataHelper.getTrader(
            id = 1.toUUID(),
            firstName = "Karel",
            lastName = "Gott",
            phone = "+************",
            biography = "Ceske ESO",
            country = Country.CZ,
            tags = listOf(TraderTag.CRYPTO),
            badgeColor = BadgeColor.GREEN_GRADIENT,
            socialLinkInstagram = "instagram-url",
            socialLinkLinkedin = "linkedind-url",
            socialLinkFacebook = "facebook-url",
            socialLinkTwitter = "twitter-url",
            calendlyUrl = "calendly-url",
            calendlyUserUri = "calendly-user-uri",
            checkoutVideoUrl = "checkout-video-url",
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )

        dataHelper.getProduct(id = 1.toUUID(), traderId = trader.id, entityModifier = { it.makeSaleable() })
        dataHelper.getProduct(id = 2.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })

        // when
        val result = underTest.handle(GetTraderMentorInfoQuery(traderId = trader.id))

        // then
        result.run {
            traderId shouldBe 1.toUUID()
            firstName shouldBe "Karel"
            lastName shouldBe "Gott"
            profilePicture shouldNotBe null
            profilePicture!!.run {
                imageOriginalUrl shouldBe "url"
                imageCompressedUrl shouldBe "url-comp"
                imageBlurHash shouldBe "123"
            }
            badgeColor shouldBe BadgeColor.GREEN_GRADIENT
            biography shouldBe "Ceske ESO"
            tags shouldBe listOf(TraderTag.CRYPTO)
            socialLinkInstagram shouldBe "instagram-url"
            socialLinkLinkedin shouldBe "linkedind-url"
            socialLinkFacebook shouldBe "facebook-url"
            socialLinkTwitter shouldBe "twitter-url"
            calendly!!.run {
                calendlyUrl shouldBe "calendly-url"
                calendlyUserUri shouldBe "calendly-user-uri"
            }
            mentoring shouldBe TraderMentoring.YES
        }
    }

    @Test
    fun `should get trader info with no mentoring`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getProduct(id = 1.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })

        // when
        val result = underTest.handle(GetTraderMentorInfoQuery(traderId = trader.id))

        // then
        result.run {
            traderId shouldBe 1.toUUID()
            mentoring shouldBe TraderMentoring.NO
        }
    }

    @Test
    fun `should throw if trader not found`() {
        dataHelper.getTrader(id = 1.toUUID())

        shouldThrow<TraderNotFoundException> {
            underTest.handle(GetTraderMentorInfoQuery(999.toUUID())) // different trader
        }
    }
}

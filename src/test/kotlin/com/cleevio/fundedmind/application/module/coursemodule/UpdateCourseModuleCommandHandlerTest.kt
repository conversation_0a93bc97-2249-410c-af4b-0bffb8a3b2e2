package com.cleevio.fundedmind.application.module.coursemodule

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.AppButtonWithLinkInput
import com.cleevio.fundedmind.application.module.coursemodule.command.UpdateCourseModuleCommand
import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.exception.EntityIsDeletedException
import com.cleevio.fundedmind.domain.coursemodule.CourseModuleRepository
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleCannotHaveBothRewardActionsException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotRelatedToCourseException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class UpdateCourseModuleCommandHandlerTest(
    @Autowired private val underTest: UpdateCourseModuleCommandHandler,
    @Autowired private val courseModuleRepository: CourseModuleRepository,
) : IntegrationTest() {

    @Test
    fun `should update course module - no coupon code or button`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            rewardCouponCode = "1234",
        )

        underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
                title = "New Module 1",
                description = "New Description",
                shortDescription = "New Short Description",
                rewardDescription = "New Reward Description",
                rewardCouponCode = null,
                rewardButton = null,
                comingSoon = true,
            ),
        )

        courseModuleRepository.findByIdOrNull(1.toUUID())!!.run {
            courseId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            title shouldBe "New Module 1"
            description shouldBe "New Description"
            shortDescription shouldBe "New Short Description"
            thumbnailPictureDesktopFileId shouldBe null
            thumbnailPictureMobileFileId shouldBe null
            rewardPictureFileId shouldBe null
            rewardDescription shouldBe "New Reward Description"
            rewardCouponCode shouldBe null
            rewardButton shouldBe null
            comingSoon shouldBe true
        }
    }

    @Test
    fun `should update course module - new coupon code`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            rewardCouponCode = "1234",
        )

        underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
                rewardCouponCode = "NEW 1234",
            ),
        )

        courseModuleRepository.findByIdOrNull(1.toUUID())!!.rewardCouponCode shouldBe "NEW 1234"
    }

    @Test
    fun `should update course module - new button`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            rewardButton = AppButtonWithLink(
                text = "Button",
                color = Color.PURPLE,
                linkUrl = "url",
            ),
        )

        underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
                rewardButton = AppButtonWithLinkInput(
                    text = "New Button",
                    color = Color.RED,
                    linkUrl = "new-url",
                ),
            ),
        )

        courseModuleRepository.findByIdOrNull(1.toUUID())!!.rewardButton!!.run {
            text shouldBe "New Button"
            color shouldBe Color.RED
            linkUrl shouldBe "new-url"
        }
    }

    @Test
    fun `should update course module - replace coupon code with button`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            rewardCouponCode = "1234",
        )

        underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
                rewardCouponCode = null,
                rewardButton = AppButtonWithLinkInput(
                    text = "Button",
                    color = Color.PURPLE,
                    linkUrl = "url",
                ),
            ),
        )

        courseModuleRepository.findByIdOrNull(1.toUUID())!!.run {
            rewardCouponCode shouldBe null
            rewardButton!!.run {
                text shouldBe "Button"
                color shouldBe Color.PURPLE
                linkUrl shouldBe "url"
            }
        }
    }

    @Test
    fun `should update course module - replace button with coupon code`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            rewardButton = AppButtonWithLink(
                text = "Button",
                color = Color.PURPLE,
                linkUrl = "url",
            ),
        )

        underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
                rewardCouponCode = "1234",
                rewardButton = null,
            ),
        )

        courseModuleRepository.findByIdOrNull(1.toUUID())!!.run {
            rewardCouponCode shouldBe "1234"
            rewardButton shouldBe null
        }
    }

    @Test
    fun `should update course module - remove button`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            rewardButton = AppButtonWithLink(
                text = "Button",
                color = Color.PURPLE,
                linkUrl = "url",
            ),
        )

        underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
                rewardCouponCode = null,
                rewardButton = null,
            ),
        )

        courseModuleRepository.findByIdOrNull(1.toUUID())!!.rewardButton shouldBe null
    }

    @Test
    fun `should update course module - remove coupon code`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            rewardCouponCode = "1234",
        )

        underTest.handle(
            defaultCommand(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
                rewardCouponCode = null,
            ),
        )

        courseModuleRepository.findByIdOrNull(1.toUUID())!!.rewardCouponCode shouldBe null
    }

    @Test
    fun `should throw if course module should have both reward coupon code and reward button`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID())

        shouldThrow<CourseModuleCannotHaveBothRewardActionsException> {
            underTest.handle(
                defaultCommand(
                    courseModuleId = 1.toUUID(),
                    courseId = 1.toUUID(),
                    rewardCouponCode = "1234",
                    rewardButton = AppButtonWithLinkInput(
                        text = "Button",
                        color = Color.PURPLE,
                        linkUrl = "url",
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw if course module not related to course`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID())

        shouldThrow<CourseModuleNotRelatedToCourseException> {
            underTest.handle(
                defaultCommand(
                    courseModuleId = 1.toUUID(),
                    courseId = 999.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if course module is deleted`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID()) { it.softDelete() }

        shouldThrow<EntityIsDeletedException> {
            underTest.handle(
                defaultCommand(
                    courseModuleId = 1.toUUID(),
                    courseId = 1.toUUID(),
                ),
            )
        }
    }

    fun defaultCommand(
        courseId: UUID,
        courseModuleId: UUID,
        title: String = "Course Module 1",
        description: String = "Description",
        shortDescription: String = "Short Description",
        rewardDescription: String? = "Reward Description",
        rewardCouponCode: String? = null,
        rewardButton: AppButtonWithLinkInput? = null,
        comingSoon: Boolean = false,
    ) = UpdateCourseModuleCommand(
        courseId = courseId,
        courseModuleId = courseModuleId,
        title = title,
        description = description,
        shortDescription = shortDescription,
        rewardDescription = rewardDescription,
        rewardCouponCode = rewardCouponCode,
        rewardButton = rewardButton,
        comingSoon = comingSoon,
    )
}

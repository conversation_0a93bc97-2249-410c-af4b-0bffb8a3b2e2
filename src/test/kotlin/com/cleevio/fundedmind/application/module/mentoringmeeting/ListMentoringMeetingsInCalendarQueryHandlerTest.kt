package com.cleevio.fundedmind.application.module.mentoringmeeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.ListMentoringMeetingsInCalendarQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.InitiatorType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate

class ListMentoringMeetingsInCalendarQueryHandlerTest(
    @Autowired private val underTest: ListMentoringMeetingsInCalendarQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list mentoring meetings in calendar if user is admin - verify mappings`() {
        // Given
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                studentId = dataHelper.getStudent().id,
                productId = dataHelper.getProduct(
                    traderId = dataHelper.getTrader(
                        id = 1.toUUID(),
                        firstName = "John",
                        lastName = "Doe",
                    ).id,
                    name = "Mentoring Product",
                ).id,
            ).id,
            color = Color.BLUE,
            startAt = "2025-04-20T10:00:00Z".toInstant(), // 20.04. 10:00
            finishAt = "2025-04-20T12:00:00Z".toInstant(), // 20.04. 12:00
            meetingUrl = "mentoring-meeting-url",
        )

        dataHelper.getMentoringMeeting(
            id = 2.toUUID(),
            mentoringId = dataHelper.getMentoring(
                studentId = dataHelper.getStudent().id,
                productId = dataHelper.getProduct(
                    traderId = dataHelper.getTrader(
                        id = 2.toUUID(),
                        firstName = "Jane",
                        lastName = "Doe",
                    ).id,
                    name = "Mentoring Product 2",
                ).id,
            ).id,
            color = Color.RED,
            startAt = "2025-04-10T10:00:00Z".toInstant(), // 10.04. 10:00
            finishAt = "2025-04-10T12:00:00Z".toInstant(), // 10.04. 12:00
            meetingUrl = "mentoring-meeting-url2",
        )

        // mentoring filtered because it is outside the filter range - before the range
        dataHelper.getMentoringMeeting(
            id = 3.toUUID(),
            mentoringId = dataHelper.getMentoring(
                studentId = dataHelper.getStudent().id,
                productId = dataHelper.getProduct(traderId = dataHelper.getTrader().id).id,
            ).id,
            startAt = "2025-05-10T10:00:00Z".toInstant(), // 10.03. 10:00
            finishAt = "2025-05-10T12:00:00Z".toInstant(), // 10.03. 12:00
        )

        // mentoring filtered because it is outside the filter range - after the range
        dataHelper.getMentoringMeeting(
            id = 4.toUUID(),
            mentoringId = dataHelper.getMentoring(
                studentId = dataHelper.getStudent().id,
                productId = dataHelper.getProduct(traderId = dataHelper.getTrader().id).id,
            ).id,
            startAt = "2025-05-10T10:00:00Z".toInstant(), // 10.05. 10:00
            finishAt = "2025-05-10T12:00:00Z".toInstant(), // 10.05. 12:00
        )

        // When
        val result = underTest.handle(
            ListMentoringMeetingsInCalendarQuery(
                userId = user.id,
                filter = ListMentoringMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30),
                ),
            ),
        )

        // Then
        result.data shouldHaveSize 2
        result.data.run {
            this[0].run {
                mentoringMeetingId shouldBe 2.toUUID()
                name shouldBe "Mentoring Product 2"
                color shouldBe Color.RED
                startAt shouldBe "2025-04-10T10:00:00Z".toInstant()
                finishAt shouldBe "2025-04-10T12:00:00Z".toInstant()
                trader.run {
                    id shouldBe 2.toUUID()
                    firstName shouldBe "Jane"
                    lastName shouldBe "Doe"
                }
            }
            this[1].run {
                mentoringMeetingId shouldBe 1.toUUID()
                name shouldBe "Mentoring Product"
                color shouldBe Color.BLUE
                startAt shouldBe "2025-04-20T10:00:00Z".toInstant()
                finishAt shouldBe "2025-04-20T12:00:00Z".toInstant()
                trader.run {
                    id shouldBe 1.toUUID()
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                }
            }
        }
    }

    @Test
    fun `should not list cancelled and rescheduled mentoring meetings`() {
        // Given
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        // Active meeting
        dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                studentId = dataHelper.getStudent().id,
                productId = dataHelper.getProduct(
                    traderId = dataHelper.getTrader().id,
                    name = "Active Meeting",
                ).id,
            ).id,
            color = Color.BLUE,
            startAt = "2025-04-20T10:00:00Z".toInstant(),
            finishAt = "2025-04-20T12:00:00Z".toInstant(),
        )

        // Cancelled meeting - should be filtered out
        val cancelledMeeting = dataHelper.getMentoringMeeting(
            id = 2.toUUID(),
            mentoringId = dataHelper.getMentoring(
                studentId = dataHelper.getStudent().id,
                productId = dataHelper.getProduct(
                    traderId = dataHelper.getTrader().id,
                    name = "Cancelled Meeting",
                ).id,
            ).id,
            color = Color.RED,
            startAt = "2025-04-15T10:00:00Z".toInstant(),
            finishAt = "2025-04-15T12:00:00Z".toInstant(),
            entityModifier = {
                it.cancel(reason = "Test cancellation", initiatorType = InitiatorType.STUDENT)
            },
        )

        // Rescheduled meeting - should be filtered out
        val rescheduledMeeting = dataHelper.getMentoringMeeting(
            id = 3.toUUID(),
            mentoringId = dataHelper.getMentoring(
                studentId = dataHelper.getStudent().id,
                productId = dataHelper.getProduct(
                    traderId = dataHelper.getTrader().id,
                    name = "Rescheduled Meeting",
                ).id,
            ).id,
            color = Color.GREEN,
            startAt = "2025-04-25T10:00:00Z".toInstant(),
            finishAt = "2025-04-25T12:00:00Z".toInstant(),
            entityModifier = {
                it.reschedule(reason = "Test reschedule", initiatorType = InitiatorType.MENTOR)
            },
        )

        // When
        val result = underTest.handle(
            ListMentoringMeetingsInCalendarQuery(
                userId = user.id,
                filter = ListMentoringMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30),
                ),
            ),
        )

        // Then
        result.data shouldHaveSize 1
        result.data.single().run {
            mentoringMeetingId shouldBe 1.toUUID()
            name shouldBe "Active Meeting"
            color shouldBe Color.BLUE
        }
    }

    @Test
    fun `trader should see only their own mentoring meetings`() {
        // Given

        // trader with his product and mentoring
        val trader = dataHelper.getTrader(id = 1.toUUID()).also { trader1 ->
            dataHelper.getAppUser(id = trader1.id, userRole = UserRole.TRADER)

            dataHelper.getProduct(
                id = 11.toUUID(),
                traderId = trader1.id,
                name = "My Product",
            ).also { product ->
                dataHelper.getMentoring(productId = product.id, studentId = dataHelper.getStudent().id).also {
                    dataHelper.getMentoringMeeting(
                        id = 11.toUUID(),
                        mentoringId = it.id,
                        color = Color.GREEN,
                        startAt = "2025-05-15T14:00:00Z".toInstant(), // 15.05. 14:00
                        finishAt = "2025-05-15T15:00:00Z".toInstant(), // 15.05. 15:00
                    )
                }
            }
        }

        // other trader with his product and mentoring
        val otherTrader = dataHelper.getTrader(id = 2.toUUID()).also { trader2 ->
            dataHelper.getAppUser(id = trader2.id, userRole = UserRole.TRADER)

            dataHelper.getProduct(
                id = 21.toUUID(),
                traderId = trader2.id,
                name = "Other Product",
            ).also { product ->
                dataHelper.getMentoring(productId = product.id, studentId = dataHelper.getStudent().id).also {
                    dataHelper.getMentoringMeeting(
                        id = 21.toUUID(),
                        mentoringId = it.id,
                        color = Color.RED,
                        startAt = "2025-05-15T16:00:00Z".toInstant(), // 15.05. 16:00
                        finishAt = "2025-05-15T17:00:00Z".toInstant(), // 15.05. 17:00
                    )
                }
            }
        }

        // When
        val result = underTest.handle(
            ListMentoringMeetingsInCalendarQuery(
                userId = trader.id,
                filter = ListMentoringMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 5, 1),
                    endDate = LocalDate.of(2025, 5, 31),
                ),
            ),
        )

        // Then
        result.data shouldHaveSize 1
        result.data.single().run {
            mentoringMeetingId shouldBe 11.toUUID()
            name shouldBe "My Product"
            color shouldBe Color.GREEN
            startAt shouldBe "2025-05-15T14:00:00Z".toInstant()
            finishAt shouldBe "2025-05-15T15:00:00Z".toInstant()
        }
    }

    @Test
    fun `student should see only their own mentoring meetings`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID()).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }
        val otherStudent = dataHelper.getStudent(id = 2.toUUID()).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        dataHelper.getTrader().also { trader ->
            dataHelper.getProduct(traderId = trader.id, name = "Product").also { product ->
                dataHelper.getMentoring(productId = product.id, studentId = student.id).also { student1Mentoring ->
                    dataHelper.getMentoringMeeting(
                        id = 1.toUUID(),
                        mentoringId = student1Mentoring.id,
                        color = Color.GREEN,
                        startAt = "2025-05-15T14:00:00Z".toInstant(), // 15.05. 14:00
                        finishAt = "2025-05-15T15:00:00Z".toInstant(), // 15.05. 15:00
                    )
                }

                dataHelper.getMentoring(productId = product.id, studentId = otherStudent.id).also { student2Mentoring ->
                    dataHelper.getMentoringMeeting(
                        id = 2.toUUID(),
                        mentoringId = student2Mentoring.id,
                        color = Color.RED,
                        startAt = "2025-05-15T16:00:00Z".toInstant(), // 15.05. 16:00
                        finishAt = "2025-05-15T17:00:00Z".toInstant(), // 15.05. 17:00
                    )
                }
            }
        }

        // When
        val result = underTest.handle(
            ListMentoringMeetingsInCalendarQuery(
                userId = student.id,
                filter = ListMentoringMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 5, 1),
                    endDate = LocalDate.of(2025, 5, 30),
                ),
            ),
        )

        // Then
        result.data shouldHaveSize 1
        result.data.single().run {
            mentoringMeetingId shouldBe 1.toUUID()
            name shouldBe "Product"
            color shouldBe Color.GREEN
            startAt shouldBe "2025-05-15T14:00:00Z".toInstant()
            finishAt shouldBe "2025-05-15T15:00:00Z".toInstant()
        }
    }
}

package com.cleevio.fundedmind.application.module.coursemodule

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.coursemodule.command.ReorderCourseModulesInCourseCommand
import com.cleevio.fundedmind.application.module.coursemodule.command.ReorderCourseModulesInCourseCommand.CourseModuleOrderingInput
import com.cleevio.fundedmind.domain.coursemodule.CourseModuleRepository
import com.cleevio.fundedmind.domain.coursemodule.exception.ActiveCourseModulesMismatchException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotFoundException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleOrderCannotBeNegativeException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ReorderCourseModulesInCourseCommandHandlerTest(
    @Autowired private val underTest: ReorderCourseModulesInCourseCommandHandler,
    @Autowired private val courseModuleRepository: CourseModuleRepository,
) : IntegrationTest() {

    @Test
    fun `should reorder courses`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID(), listingOrder = 1)
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = 1.toUUID(), listingOrder = 2)
        dataHelper.getCourseModule(id = 3.toUUID(), courseId = 1.toUUID(), listingOrder = 3)

        underTest.handle(
            ReorderCourseModulesInCourseCommand(
                courseId = 1.toUUID(),
                courseModuleOrderings = listOf(
                    CourseModuleOrderingInput(courseModuleId = 1.toUUID(), newListingOrder = 2),
                    CourseModuleOrderingInput(courseModuleId = 2.toUUID(), newListingOrder = 3),
                    CourseModuleOrderingInput(courseModuleId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        val modules = courseModuleRepository.findAll()
        modules shouldHaveSize 3
        modules.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        modules.first { it.id == 2.toUUID() }.listingOrder shouldBe 3
        modules.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should reorder modules by course id only in the given course`() {
        // given
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(id = 1.toUUID(), traderId = 1.toUUID())
        dataHelper.getCourse(id = 2.toUUID(), traderId = 1.toUUID())

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID(), listingOrder = 1)
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = 1.toUUID(), listingOrder = 2)
        // different course
        dataHelper.getCourseModule(id = 3.toUUID(), courseId = 2.toUUID(), listingOrder = 9)

        // when
        underTest.handle(
            ReorderCourseModulesInCourseCommand(
                courseId = 1.toUUID(),
                courseModuleOrderings = listOf(
                    CourseModuleOrderingInput(courseModuleId = 1.toUUID(), newListingOrder = 2),
                    CourseModuleOrderingInput(courseModuleId = 2.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val modules = courseModuleRepository.findAll()
        modules shouldHaveSize 3
        modules.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        modules.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        modules.first { it.id == 3.toUUID() }.listingOrder shouldBe 9
    }

    @Test
    fun `should throw module count by course id does not match`() {
        // given
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(id = 1.toUUID(), traderId = 1.toUUID())
        dataHelper.getCourse(id = 2.toUUID(), traderId = 1.toUUID())

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID(), listingOrder = 1)
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = 1.toUUID(), listingOrder = 2)
        dataHelper.getCourseModule(id = 3.toUUID(), courseId = 2.toUUID(), listingOrder = 3)

        shouldThrow<ActiveCourseModulesMismatchException> {
            underTest.handle(
                ReorderCourseModulesInCourseCommand(
                    courseId = 1.toUUID(),
                    // count of course orderings is 1 and expected to be 2 - because course 1 has 2 modules
                    courseModuleOrderings = listOf(
                        CourseModuleOrderingInput(courseModuleId = 1.toUUID(), newListingOrder = 2),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw module count by course id match but course is of different course`() {
        // given
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(id = 1.toUUID(), traderId = 1.toUUID())
        dataHelper.getCourse(id = 2.toUUID(), traderId = 1.toUUID())

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID(), listingOrder = 1)
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = 2.toUUID(), listingOrder = 2)

        shouldThrow<CourseModuleNotFoundException> {
            underTest.handle(
                ReorderCourseModulesInCourseCommand(
                    courseId = 1.toUUID(),
                    // count of module orderings is 1 and matches amount of modules in course 2
                    // however provided module is not in course 2
                    courseModuleOrderings = listOf(
                        CourseModuleOrderingInput(courseModuleId = 2.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should reorder courses even if display order is not unique`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID(), listingOrder = 1)
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = 1.toUUID(), listingOrder = 2)
        dataHelper.getCourseModule(id = 3.toUUID(), courseId = 1.toUUID(), listingOrder = 3)

        underTest.handle(
            ReorderCourseModulesInCourseCommand(
                courseId = 1.toUUID(),
                courseModuleOrderings = listOf(
                    CourseModuleOrderingInput(courseModuleId = 1.toUUID(), newListingOrder = 1),
                    CourseModuleOrderingInput(courseModuleId = 2.toUUID(), newListingOrder = 1),
                    CourseModuleOrderingInput(courseModuleId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        val modules = courseModuleRepository.findAll()
        modules shouldHaveSize 3
        modules.first { it.id == 1.toUUID() }.listingOrder shouldBe 1
        modules.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        modules.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should throw if there is a mismatch of courses - course is missing`() {
        // given
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID(), listingOrder = 1)
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = 1.toUUID(), listingOrder = 2)
        dataHelper.getCourseModule(id = 3.toUUID(), courseId = 1.toUUID(), listingOrder = 3)

        shouldThrow<ActiveCourseModulesMismatchException> {
            underTest.handle(
                ReorderCourseModulesInCourseCommand(
                    courseId = 1.toUUID(),
                    courseModuleOrderings = listOf(
                        CourseModuleOrderingInput(courseModuleId = 1.toUUID(), newListingOrder = 2),
                        CourseModuleOrderingInput(courseModuleId = 3.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw if display order is not positive or zero`() {
        // given
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID(), listingOrder = 1)

        shouldThrow<CourseModuleOrderCannotBeNegativeException> {
            underTest.handle(
                ReorderCourseModulesInCourseCommand(
                    courseId = 1.toUUID(),
                    courseModuleOrderings = listOf(
                        CourseModuleOrderingInput(courseModuleId = 1.toUUID(), newListingOrder = -1),
                    ),
                ),
            )
        }
    }
}

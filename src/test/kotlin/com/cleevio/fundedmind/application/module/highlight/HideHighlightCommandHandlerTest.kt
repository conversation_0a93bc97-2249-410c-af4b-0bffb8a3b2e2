package com.cleevio.fundedmind.application.module.highlight

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.highlight.command.HideHighlightCommand
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.highlight.HighlightRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class HideHighlightCommandHandlerTest(
    @Autowired private val underTest: HideHighlightCommandHandler,
    @Autowired private val highlightRepository: HighlightRepository,
) : IntegrationTest() {

    @Test
    fun `should hide highlight that was previously published`() {
        dataHelper.getHighlight(
            id = 1.toUUID(),
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.HIGHLIGHT_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.HIGHLIGHT_MOBILE_PHOTO).id)
                it.publish()
            },
        )
        highlightRepository.findByIdOrNull(1.toUUID())!!.published shouldBe true

        underTest.handle(HideHighlightCommand(highlightId = 1.toUUID()))

        highlightRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false
    }

    @Test
    fun `hiding highlight that was not published should not throw`() {
        dataHelper.getHighlight(id = 1.toUUID())

        highlightRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false

        underTest.handle(HideHighlightCommand(highlightId = 1.toUUID()))

        highlightRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false
    }
}

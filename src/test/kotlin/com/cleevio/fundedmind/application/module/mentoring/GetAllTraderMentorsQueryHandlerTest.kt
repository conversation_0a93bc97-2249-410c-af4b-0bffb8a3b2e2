package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoring.query.GetAllTraderMentorsQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.constant.TraderMentoring
import com.cleevio.fundedmind.domain.common.constant.TraderMentoringAvailability
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetAllTraderMentorsQueryHandlerTest(
    @Autowired private val underTest: GetAllTraderMentorsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get all trader mentors`() {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        dataHelper.getTrader(
            id = 1.toUUID(),
            listingOrder = 3,
            firstName = "Karel",
            lastName = "Gott",
            tags = listOf(TraderTag.MINDSET),
            mentoringAvailability = TraderMentoringAvailability.AUTOMATIC,
            entityModifier = {
                it.changeIntroPicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_INTRO_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        ).also {
            // has one saleable product - is a mentor
            dataHelper.getProduct(traderId = it.id, entityModifier = { it.makeSaleable() })
            dataHelper.getProduct(traderId = it.id, entityModifier = { it.makeUnsaleable() })

            dataHelper.getCourse(
                id = 1.toUUID(),
                traderId = it.id,
                title = "Course",
                listingOrder = 1,
                entityModifier = { it.createPicturesAndPublish() },
            ).also {
                // module with 2 lessons
                dataHelper.getCourseModule(courseId = it.id).also { module ->
                    dataHelper.getLesson(courseModuleId = module.id)
                    dataHelper.getLesson(courseModuleId = module.id)
                }
                // module with 1 lesson and 1 deleted lesson
                dataHelper.getCourseModule(courseId = it.id).also { module ->
                    dataHelper.getLesson(courseModuleId = module.id)
                    dataHelper.getLesson(courseModuleId = module.id, entityModifier = { it.softDelete() })
                }
                // deleted module with 1 deleted lesson
                dataHelper.getCourseModule(courseId = it.id, entityModifier = { it.softDelete() }).also { module ->
                    dataHelper.getLesson(courseModuleId = module.id, entityModifier = { it.softDelete() })
                }
            }
        }

        dataHelper.getTrader(
            id = 2.toUUID(),
            listingOrder = 2,
            firstName = "Joe",
            lastName = "Doe",
            tags = listOf(TraderTag.CRYPTO),
            mentoringAvailability = TraderMentoringAvailability.AUTOMATIC,
        ).also {
            // has one saleable product - is a mentor
            dataHelper.getProduct(traderId = it.id, entityModifier = { it.makeSaleable() })

            // has no course
        }

        dataHelper.getTrader(
            id = 3.toUUID(),
            listingOrder = 3,
            firstName = "Fero",
            lastName = "Ferenc",
            tags = listOf(TraderTag.LIFESTYLE),
            mentoringAvailability = TraderMentoringAvailability.AUTOMATIC,
        ).also {
            // trader has no saleable products - not a mentor
            dataHelper.getProduct(traderId = it.id, entityModifier = { it.makeUnsaleable() })
        }

        dataHelper.getTrader(
            id = 4.toUUID(),
            listingOrder = 4,
            firstName = "Ignac",
            lastName = "Istvanovsky",
            tags = listOf(TraderTag.SCALPING),
            mentoringAvailability = TraderMentoringAvailability.PAUSED,
        ).also {
            dataHelper.getProduct(traderId = it.id, entityModifier = { it.makeSaleable() })
        }

        // when
        val result = underTest.handle(GetAllTraderMentorsQuery(userId = user.id))

        // then
        result.data shouldHaveSize 3
        result.data.run {
            this[0].run {
                traderId shouldBe 2.toUUID()
                firstName shouldBe "Joe"
                lastName shouldBe "Doe"
                introPicture shouldBe null
                traderCourse shouldBe null
                tags shouldBe listOf(TraderTag.CRYPTO)
                isMentoringLockedForMe shouldBe false
                mentoring shouldBe TraderMentoring.YES
            }
            this[1].run {
                traderId shouldBe 1.toUUID()
                firstName shouldBe "Karel"
                lastName shouldBe "Gott"
                introPicture shouldNotBe null
                introPicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                traderCourse shouldNotBe null
                traderCourse!!.run {
                    courseId shouldBe 1.toUUID()
                    courseName shouldBe "Course"
                    lessonCount shouldBe 3
                }
                tags shouldBe listOf(TraderTag.MINDSET)
                isMentoringLockedForMe shouldBe false
                mentoring shouldBe TraderMentoring.YES
            }
            this[2].run {
                traderId shouldBe 4.toUUID()
                firstName shouldBe "Ignac"
                lastName shouldBe "Istvanovsky"
                introPicture shouldBe null
                traderCourse shouldBe null
                tags shouldBe listOf(TraderTag.SCALPING)
                isMentoringLockedForMe shouldBe false
                mentoring shouldBe TraderMentoring.PAUSED
            }
        }
    }

    @Test
    fun `should get all trader mentors - verify what trader course is chosen`() {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        dataHelper.getTrader(id = 1.toUUID()).also {
            // saleable product to make trader a mentor
            dataHelper.getProduct(traderId = it.id, entityModifier = { it.makeSaleable() })

            dataHelper.getCourse(
                id = 1.toUUID(),
                traderId = it.id,
                title = "Course 1",
                listingOrder = 2,
                entityModifier = { it.createPicturesAndPublish() },
            ).also {
                dataHelper.getCourseModule(courseId = it.id).also { module ->
                    dataHelper.getLesson(courseModuleId = module.id)
                    dataHelper.getLesson(courseModuleId = module.id)
                }
                dataHelper.getCourseModule(courseId = it.id).also { module ->
                    dataHelper.getLesson(courseModuleId = module.id)
                }
            }

            dataHelper.getCourse(
                id = 2.toUUID(),
                traderId = it.id,
                title = "Course 2",
                listingOrder = 1,
                entityModifier = { it.createPicturesAndPublish() },
            ).also {
                dataHelper.getCourseModule(courseId = it.id).also { module ->
                    dataHelper.getLesson(courseModuleId = module.id)
                }
            }
        }

        // when
        val result = underTest.handle(GetAllTraderMentorsQuery(userId = user.id))

        // then
        result.data shouldHaveSize 1
        result.data.first().run {
            traderCourse shouldNotBe null
            traderCourse!!.run {
                courseId shouldBe 2.toUUID()
                courseName shouldBe "Course 2"
                lessonCount shouldBe 1
            }
        }
    }

    @Test
    fun `should get all trader mentors with locked flag if student tier is Basecamp`() {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP)
        }

        dataHelper.getTrader(id = 1.toUUID()).also {
            // saleable product to make trader a mentor
            dataHelper.getProduct(traderId = it.id, entityModifier = { it.makeSaleable() })

            dataHelper.getCourse(
                id = 1.toUUID(),
                traderId = it.id,
                title = "Course 1",
                listingOrder = 2,
                entityModifier = { it.createPicturesAndPublish() },
            ).also {
                dataHelper.getCourseModule(courseId = it.id).also { module ->
                    dataHelper.getLesson(courseModuleId = module.id)
                    dataHelper.getLesson(courseModuleId = module.id)
                }
                dataHelper.getCourseModule(courseId = it.id).also { module ->
                    dataHelper.getLesson(courseModuleId = module.id)
                }
            }
        }

        // when
        val result = underTest.handle(GetAllTraderMentorsQuery(userId = user.id))

        // then
        result.data shouldHaveSize 1
        result.data.first().run {
            traderId shouldBe 1.toUUID()
            traderCourse shouldNotBe null
            isMentoringLockedForMe shouldBe true
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}

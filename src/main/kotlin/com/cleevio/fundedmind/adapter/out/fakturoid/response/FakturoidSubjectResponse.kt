package com.cleevio.fundedmind.adapter.out.fakturoid.response

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Response model for Fakturoid subject.
 * Based on https://www.fakturoid.cz/api/v3/#subject
 */
data class FakturoidSubjectResponse(
    val id: Long,
    val name: String,

    @JsonProperty("custom_id")
    val customId: String?,

    val type: String,
    val email: String?,
    val street: String?,
    val city: String?,
    val zip: String?,
    val country: String?,

    @JsonProperty("registration_no")
    val registrationNo: String?,

    @JsonProperty("vat_no")
    val vatNo: String?,

    @JsonProperty("html_url")
    val htmlUrl: String,

    @JsonProperty("url")
    val apiUrl: String,

    @JsonProperty("updated_at")
    val updatedAt: String,

    @JsonProperty("created_at")
    val createdAt: String,
)

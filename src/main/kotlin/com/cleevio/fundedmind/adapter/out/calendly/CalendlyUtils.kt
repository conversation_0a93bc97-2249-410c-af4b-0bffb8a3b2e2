package com.cleevio.fundedmind.adapter.out.calendly

import com.cleevio.fundedmind.application.common.type.CalendlyEventUri
import com.cleevio.fundedmind.application.common.type.CalendlyWebhookUri
import com.cleevio.fundedmind.application.common.util.toUUID
import java.util.UUID

fun CalendlyEventUri.extractEventUuid(): UUID {
    val uri = this.toString()

    // Find the part after "scheduled_events/"
    val index = uri.lastIndexOf("/scheduled_events/") + "/scheduled_events/".length

    // Return everything after the "scheduled_events/" part
    return uri.substring(index).toUUID()
}

fun CalendlyWebhookUri.extractWebhookUuid(): UUID {
    val uri = this.toString()

    // Find the part after "webhook_subscriptions/"
    val index = uri.lastIndexOf("/webhook_subscriptions/") + "/webhook_subscriptions/".length

    // Return everything after the "webhook_subscriptions/" part
    return uri.substring(index).toUUID()
}

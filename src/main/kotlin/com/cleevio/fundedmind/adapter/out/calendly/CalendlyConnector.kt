package com.cleevio.fundedmind.adapter.out.calendly

import com.cleevio.fundedmind.adapter.out.BaseConnector
import com.cleevio.fundedmind.adapter.out.calendly.request.CalendlyCancelScheduledEventRequest
import com.cleevio.fundedmind.adapter.out.calendly.request.CalendlyCreateWebhookRequest
import com.cleevio.fundedmind.adapter.out.calendly.response.CalendlyCancelScheduledEventResponse
import com.cleevio.fundedmind.adapter.out.calendly.response.CalendlyCreateWebhookResponse
import com.cleevio.fundedmind.adapter.out.calendly.response.CalendlyListUsersResponse
import com.cleevio.fundedmind.adapter.out.calendly.response.CalendlyListWebhooksResponse
import com.cleevio.fundedmind.application.common.type.CalendlyEventUri
import com.cleevio.fundedmind.application.common.type.CalendlyWebhookUri
import com.cleevio.fundedmind.infrastructure.properties.CalendlyProperties
import com.cleevio.fundedmind.infrastructure.properties.WebhookSecurityProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnBooleanProperty
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Component

@Component
@ConditionalOnBooleanProperty(name = ["integration.calendly.enabled"], havingValue = true)
class CalendlyConnector(
    private val calendlyProperties: CalendlyProperties,
    private val webhookSecurityProperties: WebhookSecurityProperties,
) : BaseConnector(
    baseUrl = calendlyProperties.baseUrl,
    restClientCustomizer = {
        it.defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer ${calendlyProperties.apiKey}")
    },
) {

    /**
     * See https://developer.calendly.com/api-docs/eaed2e61a6bc3-list-organization-memberships
     */
    fun listAllOrganizationUsers(): CalendlyListUsersResponse {
        logger.debug("Listing all Calendly users...")

        // use 100 because it is both the max allowed size, and we don't expect more than 100 traders on the platform
        val count = 100

        val response = restClient
            .get()
            .uriAndHeaders(
                path = "/organization_memberships",
                queryParams = mapOf(
                    "count" to listOf(count),
                    "organization" to listOf(calendlyProperties.organization),
                    "scope" to listOf("organization"),
                ),
            )
            .retrieveResponseWithErrorHandler<CalendlyListUsersResponse>()

        logger.debug("Fetched ${response.collection.size} users.")

        return response
    }

    fun cancelScheduledEvent(
        scheduledEventUri: CalendlyEventUri,
        reason: String,
    ): CalendlyCancelScheduledEventResponse {
        logger.debug("Cancelling scheduled event...")

        require(reason.length <= 10_000) { "Calendly doesn't allow reason longer than 10,000 characters." }
        require(reason.isNotBlank()) { "Reason is attached to the user's email and its not nice not to provide one." }

        val request = CalendlyCancelScheduledEventRequest(reason = reason)

        val scheduledEventId = scheduledEventUri.extractEventUuid()

        val response = restClient
            .post()
            .uriAndHeaders(
                path = "/scheduled_events/$scheduledEventId/cancellation",
            )
            .jsonBody(request)
            .retrieveResponseWithErrorHandler<CalendlyCancelScheduledEventResponse>()

        logger.debug("Calendly scheduled event: '$scheduledEventId' cancelled with reason: ${response.resource.reason}")

        return response
    }

    /**
     * https://developer.calendly.com/api-docs/faac832d7c57d-list-webhook-subscriptions
     */
    fun listWebhooks(): CalendlyListWebhooksResponse {
        logger.debug("Fetching Calendly webhooks...")

        val response = restClient
            .get()
            .uriAndHeaders(
                path = "/webhook_subscriptions",
                queryParams = mapOf(
                    "scope" to listOf("organization"),
                    "organization" to listOf(calendlyProperties.organization),
                ),
            )
            .retrieveResponseWithErrorHandler<CalendlyListWebhooksResponse>()

        logger.debug("Fetched ${response.collection.size} webhooks.")

        return response
    }

    /**
     * https://developer.calendly.com/api-docs/c1ddc06ce1f1b-create-webhook-subscription
     */
    fun createWebhook(): CalendlyCreateWebhookResponse {
        logger.debug("Creating Calendly webhook...")

        val request = CalendlyCreateWebhookRequest(
            url = calendlyProperties.webhookUrl,
            events = listOf("invitee.created", "invitee.canceled"),
            organization = calendlyProperties.organization,
            scope = "organization",
            signingKey = webhookSecurityProperties.calendly.secretKey,
        )

        val response = restClient
            .post()
            .uriAndHeaders("/webhook_subscriptions")
            .jsonBody(request)
            .retrieveResponseWithErrorHandler<CalendlyCreateWebhookResponse>()

        logger.debug("Calendly webhook created: '${response.resource.uri}'.")

        return response
    }

    /**
     * https://developer.calendly.com/api-docs/565b97f62dafe-delete-webhook-subscription
     */
    fun deleteWebhook(webhookUri: CalendlyWebhookUri) {
        logger.debug("Deleting Calendly webhook: '$webhookUri'...")

        val webhookId = webhookUri.extractWebhookUuid()

        restClient
            .delete()
            .uriAndHeaders("/webhook_subscriptions/$webhookId")
            .retrieveWithErrorHandler()

        logger.debug("Calendly webhook deleted: '$webhookUri'.")
    }
}

package com.cleevio.fundedmind.adapter.out.calendly

import com.cleevio.fundedmind.adapter.out.calendly.response.CalendlyListWebhooksResponse.WebhookSubscription
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.fundedmind.infrastructure.properties.CalendlyProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnBooleanProperty
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Profile
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnBooleanProperty(name = ["integration.calendly.enabled"], havingValue = true)
@Profile("!test")
class CalendlyWebhookRegistration(
    private val calendlyConnector: CalendlyConnector,
    private val calendlyProperties: CalendlyProperties,
) {
    private val logger = logger()

    @EventListener(ApplicationReadyEvent::class)
    fun onApplicationReady() {
        runCatching {
            logger.info("Verifying presence of Calendly webhooks...")
            val webhooks = calendlyConnector.listWebhooks().collection

            when {
                webhooks.isEmpty() -> {
                    logger.info("No Calendly webhooks found...")
                    createFundedMindWebhook()
                }

                webhooks.size > 1 -> {
                    logger.warn("Found ${webhooks.size} Calendly webhooks! ${webhooks.map { it.callbackUrl }}")
                    processExistingWebhooks(webhooks)
                }

                else -> processExistingWebhooks(webhooks)
            }
        }.onFailure {
            logger.error("Failed to register Calendly webhooks, but continuing application startup: $it", it)
        }
    }

    private fun processExistingWebhooks(webhooks: List<WebhookSubscription>) {
        val matchingWebhook = webhooks.firstOrNull { it.callbackUrl == calendlyProperties.webhookUrl }

        if (matchingWebhook == null) {
            logger.warn("Calendly webhook was not found! Webhooks found: ${webhooks.map { it.callbackUrl }}")
            createFundedMindWebhook()
            return
        }

        logger.info(
            "Calendly webhook was successfully found: '${matchingWebhook.uri}': ${matchingWebhook.callbackUrl}, " +
                "created at: '${matchingWebhook.createdAt}'.",
        )

        if (matchingWebhook.state != "active") {
            logger.info("Calendly webhook is not active - deleting it and creating a new one...")
            calendlyConnector.deleteWebhook(matchingWebhook.uri)
            createFundedMindWebhook()
        }
    }

    private fun createFundedMindWebhook() {
        logger.info("Creating Calendly webhook '${calendlyProperties.webhookUrl}'...")
        val response = calendlyConnector.createWebhook()
        logger.info("Calendly webhook created, URI: '${response.resource.uri}'.")
    }
}

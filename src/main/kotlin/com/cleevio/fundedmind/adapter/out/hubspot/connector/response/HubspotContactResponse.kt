package com.cleevio.fundedmind.adapter.out.hubspot.connector.response

import com.cleevio.fundedmind.adapter.out.hubspot.connector.HubspotContactProperties
import com.cleevio.fundedmind.application.common.type.HubspotId
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class HubspotContactResponse(
    @JsonProperty("id") val hubspotIdentifier: HubspotId,
    val properties: HubspotContactProperties,
)

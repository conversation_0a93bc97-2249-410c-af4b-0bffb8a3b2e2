package com.cleevio.fundedmind.adapter.out.calendly.request

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * see https://developer.calendly.com/api-docs/c1ddc06ce1f1b-create-webhook-subscription
 */
data class CalendlyCreateWebhookRequest(
    val url: String,
    val events: List<String>,
    val organization: String,
    val scope: String,
    @JsonProperty("signing_key") val signingKey: String,
)

/**
 * see https://developer.calendly.com/api-docs/afb2e9fe3a0a0-cancel-event
 */
data class CalendlyCancelScheduledEventRequest(
    val reason: String,
)

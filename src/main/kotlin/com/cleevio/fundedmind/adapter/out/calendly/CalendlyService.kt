package com.cleevio.fundedmind.adapter.out.calendly

import com.cleevio.fundedmind.application.common.port.out.CancelCalendlyEventPort
import com.cleevio.fundedmind.application.common.port.out.ListCalendlyTradersPort
import com.cleevio.fundedmind.application.common.type.CalendlyEventUri
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.boot.autoconfigure.condition.ConditionalOnBooleanProperty
import org.springframework.stereotype.Service

@Service
@ConditionalOnBooleanProperty(name = ["integration.calendly.enabled"], havingValue = true)
class CalendlyService(
    private val calendlyConnector: CalendlyConnector,
) : ListCalendlyTradersPort, CancelCalendlyEventPort {

    override fun listCalendlyTraders(): List<ListCalendlyTradersPort.CalendlyTrader> {
        val allCalendlyUsers = calendlyConnector.listAllOrganizationUsers().collection

        return allCalendlyUsers.map {
            ListCalendlyTradersPort.CalendlyTrader(
                name = it.user.name,
                calendlyUrl = it.user.schedulingUrl,
                email = it.user.email,
                calendlyUserUri = it.user.uri,
            )
        }
    }

    override fun cancelCalendlyEvent(
        scheduledEventUri: CalendlyEventUri,
        reason: String,
    ) {
        calendlyConnector.cancelScheduledEvent(
            scheduledEventUri = scheduledEventUri,
            reason = reason,
        )
    }
}

@Service
@ConditionalOnBooleanProperty(name = ["integration.calendly.enabled"], havingValue = false)
class DummyCalendlyService : ListCalendlyTradersPort, CancelCalendlyEventPort {

    private val logger = logger()

    override fun listCalendlyTraders(): List<ListCalendlyTradersPort.CalendlyTrader> {
        logger.trace("Listing Calendly users is disabled.")
        return emptyList()
    }

    override fun cancelCalendlyEvent(
        scheduledEventUri: CalendlyEventUri,
        reason: String,
    ) {
        logger.trace("Cancelling Calendly event is disabled.")
    }
}

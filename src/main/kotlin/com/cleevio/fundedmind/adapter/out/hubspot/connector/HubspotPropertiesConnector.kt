package com.cleevio.fundedmind.adapter.out.hubspot.connector

import com.cleevio.fundedmind.adapter.out.BaseConnector
import com.cleevio.fundedmind.adapter.out.hubspot.connector.request.HubspotPropertyRequest
import com.cleevio.fundedmind.infrastructure.properties.HubspotProperties
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Component

@Component
class HubspotPropertiesConnector(hubspotProperties: HubspotProperties) : BaseConnector(
    baseUrl = "${hubspotProperties.baseUrl}/properties",
    restClientCustomizer = {
        it.defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer ${hubspotProperties.apiKey}")
    },
) {

    fun createCustomerProperty(request: HubspotPropertyRequest): HubspotPropertyResponse = restClient.post()
        .uriAndHeaders("/contacts")
        .jsonBody(request)
        .retrieveResponseWithErrorHandler<HubspotPropertyResponse>()
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class HubspotPropertyResponse(
    val name: String,
)

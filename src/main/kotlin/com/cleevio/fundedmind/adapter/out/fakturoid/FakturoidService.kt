package com.cleevio.fundedmind.adapter.out.fakturoid

import com.cleevio.fundedmind.adapter.out.fakturoid.request.FakturoidInvoiceLineRequest
import com.cleevio.fundedmind.adapter.out.fakturoid.request.FakturoidInvoiceRequest
import com.cleevio.fundedmind.adapter.out.fakturoid.request.FakturoidPaymentRequest
import com.cleevio.fundedmind.adapter.out.fakturoid.request.FakturoidSubjectRequest
import com.cleevio.fundedmind.adapter.out.fakturoid.response.FakturoidSubjectResponse
import com.cleevio.fundedmind.application.common.type.FakturoidInvoiceId
import com.cleevio.fundedmind.application.common.type.StripeInvoiceId
import com.cleevio.fundedmind.application.module.payment.port.out.InvoicingPort
import com.cleevio.fundedmind.domain.common.constant.TaxBehaviour
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.stereotype.Service
import java.time.LocalDate

/**
 * Service implementation of the InvoicingPort that uses Fakturoid for invoice generation.
 */
@Service
class FakturoidService(
    private val fakturoidConnector: FakturoidConnector,
) : InvoicingPort {

    private val logger = logger()

    override fun findInvoiceIdByCustomId(customId: StripeInvoiceId): InvoicingPort.InvoiceResponse? = fakturoidConnector
        .findInvoiceByCustomId(customId)
        ?.let {
            InvoicingPort.InvoiceResponse(
                id = it.id,
                status = it.status,
            )
        }

    override fun createInvoice(request: InvoicingPort.InvoiceCreationRequest): FakturoidInvoiceId {
        logger.debug("Creating invoice for '${request.name}' (${request.email})")

        val subject = getOrCreateSubject(request)

        // Create invoice lines from products
        val invoiceLines = request.products.map { product ->
            FakturoidInvoiceLineRequest(
                name = product.name,
                quantity = "1",
                unitName = "ks",
                unitPrice = product.price,
                vatRate = product.vatRate,
            )
        }

        // Determine VAT price mode based on tax behavior of the first product with tax behavior
        val vatPriceMode = request.products
            .firstOrNull { it.taxBehaviour != null }
            ?.taxBehaviour
            ?.let {
                when (it) {
                    TaxBehaviour.INCLUSIVE -> "from_total_with_vat"
                    TaxBehaviour.EXCLUSIVE -> "without_vat"
                }
            }

        // Create the invoice
        val invoice = fakturoidConnector.createInvoice(
            FakturoidInvoiceRequest(
                customId = request.customId,
                subjectId = subject.id,
                issuedOn = request.issuedOn,
                lines = invoiceLines,
                due = 1,
                vatPriceMode = vatPriceMode,
            ),
        )

        logger.debug("Invoice created successfully with ID: ${invoice.id}")

        return invoice.id
    }

    override fun payInvoice(
        invoiceId: FakturoidInvoiceId,
        paidAt: LocalDate,
    ) {
        logger.debug("Marking invoice: '$invoiceId' as paid on $paidAt.")

        // Create a payment for the invoice
        fakturoidConnector.createPaymentByInvoiceId(
            invoiceId = invoiceId,
            request = FakturoidPaymentRequest(
                sendThankYouEmail = false,
                paidOn = paidAt,
            ),
        )

        logger.debug("Successfully marked invoice: '$invoiceId' as paid.")
    }

    private fun getOrCreateSubject(request: InvoicingPort.InvoiceCreationRequest): FakturoidSubjectResponse =
        fakturoidConnector
            .searchSubjectsByEmail(request.email)
            .firstOrNull()
            ?: createNewSubject(request)

    private fun createNewSubject(request: InvoicingPort.InvoiceCreationRequest): FakturoidSubjectResponse {
        val baseRequest = FakturoidSubjectRequest(
            type = "customer",
            name = request.name,
            customId = request.customId,
            email = request.email,
            street = request.street,
            city = request.city,
            zip = request.zip,
            country = request.country,
            registrationNo = request.ico,
            vatNo = null,
            localVatNo = null,
        )

        // Handle Slovakia specific logic for DIČ (vatNo) a IČ DPH (localVatNo)
        val preparedRequest = if (request.country == "SK" && request.dic?.startsWith("SK") == false) {
            baseRequest.copy(localVatNo = request.dic)
        } else {
            baseRequest.copy(vatNo = request.dic)
        }

        return fakturoidConnector.createSubject(preparedRequest)
    }
}

package com.cleevio.fundedmind.adapter.out.discord.request

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Request object for adding a user to a guild
 * See https://discord.com/developers/docs/resources/guild#add-guild-member
 */
data class DiscordAddGuildMemberRequest(
    // OAuth2 access token of the user to add
    @JsonProperty("access_token")
    val accessToken: String,

    // Value to set the user's nickname to
    val nick: String? = null,

    // Array of role IDs the user is assigned
    val roles: List<String>? = null,
)

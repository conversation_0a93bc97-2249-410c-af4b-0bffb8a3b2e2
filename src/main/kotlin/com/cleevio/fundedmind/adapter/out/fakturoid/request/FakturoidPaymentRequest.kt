package com.cleevio.fundedmind.adapter.out.fakturoid.request

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDate

/**
 * Request model for creating a payment for a Fakturoid invoice.
 * Based on https://www.fakturoid.cz/api/v3/invoice-payments#create-payment
 */
data class FakturoidPaymentRequest(
    @JsonProperty("send_thank_you_email")
    val sendThankYouEmail: Boolean = false,

    @JsonProperty("paid_on")
    val paidOn: LocalDate,
)

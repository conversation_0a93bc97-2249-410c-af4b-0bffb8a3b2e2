package com.cleevio.fundedmind.adapter.out.hubspot.connector

import com.cleevio.fundedmind.adapter.out.BaseConnector
import com.cleevio.fundedmind.adapter.out.hubspot.connector.request.HubspotContactRequest
import com.cleevio.fundedmind.adapter.out.hubspot.connector.request.HubspotSearchRequest
import com.cleevio.fundedmind.adapter.out.hubspot.connector.response.HubspotContactResponse
import com.cleevio.fundedmind.adapter.out.hubspot.connector.response.HubspotContactSearchResponse
import com.cleevio.fundedmind.application.common.type.HubspotId
import com.cleevio.fundedmind.infrastructure.properties.HubspotProperties
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Component

@Component
class HubspotContactConnector(
    private val objectMapper: ObjectMapper,
    hubspotProperties: HubspotProperties,
) : BaseConnector(
    baseUrl = "${hubspotProperties.baseUrl}/objects/contacts",
    restClientCustomizer = {
        it.defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer ${hubspotProperties.apiKey}")
    },
) {

    fun getByEmail(request: HubspotSearchRequest): HubspotContactSearchResponse = restClient.post()
        .uriAndHeaders("/search")
        .jsonBody(request)
        .retrieveResponseWithErrorHandler<HubspotContactSearchResponse>()

    fun create(hubspotContactProps: HubspotContactProperties): HubspotContactResponse {
        val request = HubspotContactRequest(
            association = listOf(
                HubspotContactRequest.Association.ofEmpty(),
            ),
            properties = objectMapper.writeValueAsString(hubspotContactProps),
        )

        return restClient.post()
            .uriAndHeaders("")
            .jsonBody(request)
            .retrieveResponseWithErrorHandler<HubspotContactResponse>()
    }

    fun update(
        hubspotIdentifier: HubspotId,
        hubspotContactProp: HubspotContactProperties,
    ): HubspotContactResponse = restClient
        .patch()
        .uri("/$hubspotIdentifier")
        .jsonBody(
            HubspotContactRequest(
                association = emptyList(),
                properties = objectMapper.writeValueAsString(hubspotContactProp),
            ),
        )
        .retrieveResponseWithErrorHandler<HubspotContactResponse>()

    fun update(
        hubspotIdentifier: HubspotId,
        hubspotContactPropJson: Map<String, Any>,
    ): HubspotContactResponse = restClient
        .patch()
        .uri("/$hubspotIdentifier")
        .jsonBody(
            HubspotContactRequest(
                association = emptyList(),
                properties = objectMapper.writeValueAsString(hubspotContactPropJson),
            ),
        )
        .retrieveResponseWithErrorHandler<HubspotContactResponse>()
}

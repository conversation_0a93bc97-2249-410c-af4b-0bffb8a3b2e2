package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.module.gamedocument.port.out.StudentGameProgressPort
import com.cleevio.fundedmind.application.module.user.student.exception.StudentNotFoundException
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.jooq.tables.references.COURSE
import com.cleevio.fundedmind.jooq.tables.references.COURSE_PROGRESS
import com.cleevio.fundedmind.jooq.tables.references.GAME_DOCUMENT
import com.cleevio.fundedmind.jooq.tables.references.STUDENT
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

@Component
class StudentGameProgressJooq(
    private val dslContext: DSLContext,
) : StudentGameProgressPort {

    override fun getStudentGameProgress(studentId: UUID): StudentGameProgressPort.StudentGameProgress {
        val studentRecord = dslContext
            .select(
                STUDENT.FIRST_NAME,
                STUDENT.LAST_NAME,
                STUDENT.GAME_LEVEL,
            )
            .from(STUDENT)
            .where(STUDENT.ID.eq(studentId))
            .fetchOne()
            ?: throw StudentNotFoundException("Student: '$studentId' not found.")

        val gameDocuments = fetchStudentGameDocuments(studentId)

        // Calculate overall payout amount (sum of all approved payouts)
        val overallPayoutAmount = gameDocuments
            .filter { doc: GameDocumentData ->
                doc.type == GameDocumentType.PAYOUT &&
                    doc.state == GameDocumentApprovalState.APPROVED &&
                    doc.payoutAmount != null &&
                    doc.approvedAt != null
            }
            .sumOf { doc: GameDocumentData -> doc.payoutAmount ?: BigDecimal.ZERO }

        val backtesting = gameDocuments
            .firstOrNull { doc: GameDocumentData -> doc.type == GameDocumentType.BACKTESTING }
            ?.let { doc: GameDocumentData -> doc.toInfo() }

        val certificate = gameDocuments
            .firstOrNull { doc: GameDocumentData -> doc.type == GameDocumentType.CERTIFICATE }
            ?.let { doc: GameDocumentData -> doc.toInfo() }

        val firstPayout = gameDocuments
            .firstOrNull { doc: GameDocumentData -> doc.type == GameDocumentType.PAYOUT }
            ?.let { doc: GameDocumentData -> doc.toInfo() }

        // Check if student has completed a strategy course
        val hasStrategyModuleFinished = dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(COURSE_PROGRESS)
                .join(COURSE).on(COURSE_PROGRESS.COURSE_ID.eq(COURSE.ID))
                .where(
                    COURSE_PROGRESS.USER_ID.eq(studentId),
                    COURSE_PROGRESS.FINISHED.isTrue,
                    COURSE.COURSE_CATEGORY.eq(CourseCategory.STRATEGY),
                    COURSE.DELETED_AT.isNull,
                ),
        )

        return StudentGameProgressPort.StudentGameProgress(
            firstName = studentRecord.get(STUDENT.FIRST_NAME)!!,
            lastName = studentRecord.get(STUDENT.LAST_NAME)!!,
            gameLevel = studentRecord.get(STUDENT.GAME_LEVEL)!!,
            overallPayoutAmount = overallPayoutAmount,
            backtesting = backtesting,
            hasStrategyModuleFinished = hasStrategyModuleFinished,
            certificate = certificate,
            firstPayout = firstPayout,
        )
    }

    private fun fetchStudentGameDocuments(studentId: UUID): List<GameDocumentData> = dslContext
        .select(
            GAME_DOCUMENT.ID,
            GAME_DOCUMENT.TYPE,
            GAME_DOCUMENT.STATE,
            GAME_DOCUMENT.PAYOUT_AMOUNT,
            GAME_DOCUMENT.APPROVED_AT,
        )
        .from(GAME_DOCUMENT)
        .where(GAME_DOCUMENT.STUDENT_ID.eq(studentId))
        .fetch()
        .map { record ->
            GameDocumentData(
                id = record.get(GAME_DOCUMENT.ID)!!,
                type = record.get(GAME_DOCUMENT.TYPE)!!,
                state = record.get(GAME_DOCUMENT.STATE)!!,
                payoutAmount = record.get(GAME_DOCUMENT.PAYOUT_AMOUNT),
                approvedAt = record.get(GAME_DOCUMENT.APPROVED_AT),
            )
        }

    private data class GameDocumentData(
        val id: UUID,
        val type: GameDocumentType,
        val state: GameDocumentApprovalState,
        val payoutAmount: BigDecimal?,
        val approvedAt: Instant?,
    )

    private fun GameDocumentData.toInfo() = StudentGameProgressPort.GameDocumentInfo(
        gameDocumentId = id,
        state = state,
    )
}

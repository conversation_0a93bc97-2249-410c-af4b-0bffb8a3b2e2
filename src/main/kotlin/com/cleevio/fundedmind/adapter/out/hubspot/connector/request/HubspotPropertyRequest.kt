package com.cleevio.fundedmind.adapter.out.hubspot.connector.request

data class HubspotPropertyRequest(
    val name: String,
    val label: String,
    val type: String,
    val fieldType: String,
    val groupName: String = "contactinformation", // not important default Hubspot group,
    val description: String? = null,
    val options: List<HubspotPropertyOption>? = null, // List of options, nullable
) {
    companion object {
        val booleanOptions: List<HubspotPropertyOption>
            get() = listOf(booleanOptionTrue, booleanOptionFalse)

        private val booleanOptionTrue: HubspotPropertyOption
            get() = HubspotPropertyOption(label = "ANO", value = "true")
        private val booleanOptionFalse: HubspotPropertyOption
            get() = HubspotPropertyOption(label = "NE", value = "false")
    }

    // Data class for individual property options
    data class HubspotPropertyOption(
        val label: String,
        val value: String,
        val displayOrder: Int? = null,
        val hidden: Boolean? = false,
    )
}

enum class HubspotFieldType(val type: String, val fieldType: String) {
    NUMBER("number", "text"),
    STRING("string", "text"),
    CHECKBOX("bool", "booleancheckbox"),
}

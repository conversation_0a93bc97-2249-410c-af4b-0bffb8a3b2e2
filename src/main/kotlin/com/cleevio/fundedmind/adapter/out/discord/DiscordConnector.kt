package com.cleevio.fundedmind.adapter.out.discord

import com.cleevio.fundedmind.adapter.out.BaseConnector
import com.cleevio.fundedmind.adapter.out.IntegrationException
import com.cleevio.fundedmind.adapter.out.discord.request.DiscordAddGuildMemberRequest
import com.cleevio.fundedmind.adapter.out.discord.request.DiscordPatchGuildMemberRolesRequest
import com.cleevio.fundedmind.adapter.out.discord.response.DiscordGuildMemberResponse
import com.cleevio.fundedmind.infrastructure.properties.DiscordProperties
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.client.RestClient
import java.io.BufferedReader
import kotlin.time.Duration.Companion.seconds

@Component
class DiscordConnector(discordProperties: DiscordProperties) : BaseConnector(
    baseUrl = discordProperties.baseUrl,
    restClientCustomizer = {
        it.defaultHeader(HttpHeaders.AUTHORIZATION, "Bot ${discordProperties.clientAppAccessToken}")
        it.defaultHeader(HttpHeaders.USER_AGENT, "DiscordBot (FundedMind, 1.0.0)")
    },
) {
    private val guildId = discordProperties.guildId
    private val studentRoleId = discordProperties.studentRoleId
    private val entryRoleId = discordProperties.entryRoleId

    /**
     * Get a guild member by ID
     * See https://discord.com/developers/docs/resources/guild#get-guild-member
     *
     * @param userId The ID of the member to get
     * @return The guild member information
     */
    fun getGuildMember(userId: String): DiscordGuildMemberResponse {
        val response = restClient
            .get()
            .uriAndHeaders(path = "/guilds/$guildId/members/$userId")
            .retrieveResponseWithErrorHandler<DiscordGuildMemberResponse>()

        logger.debug("Successfully retrieved Discord guild member with ID: $userId")

        return response
    }

    /**
     * Add a user to a guild
     * See https://discord.com/developers/docs/resources/guild#add-guild-member
     *
     * @param userId The ID of the user to add to the guild
     * @param accessToken OAuth2 access token of the user to add
     * @param nick Value to set the user's nickname to (optional)
     * @param roles Array of role IDs the user is assigned (entry role is alwais assigned, studentRole is optional )
     * @return The guild member object if successful, null if the user is already a member of the guild
     */
    fun addGuildMember(
        userId: String,
        accessToken: String,
        firstName: String?,
        lastName: String?,
        hasPremiumDiscordAccess: Boolean,
    ): DiscordGuildMemberResponse? {
        val request = DiscordAddGuildMemberRequest(
            accessToken = accessToken,
            nick = listOfNotNull(firstName, lastName).joinToString(" "),
            roles = if (hasPremiumDiscordAccess) {
                listOf(studentRoleId, entryRoleId)
            } else {
                listOf(entryRoleId)
            },
        )

        return restClient
            .put()
            .uriAndHeaders(path = "/guilds/$guildId/members/$userId")
            .jsonBody(request)
            .retrieveResponseWithErrorHandler(HttpStatus.NO_CONTENT)
    }

    /**
     * Add a specific role to a guild member
     * See https://discord.com/developers/docs/resources/guild#add-guild-member-role
     *
     * @param userId The ID of the user to add the role to
     * @param roleId The ID of the role to add
     */
    fun addRoleToGuildMember(
        userId: String,
        roleId: String,
    ) {
        executeWithWaitForRateLimit {
            restClient
                .put()
                .uriAndHeaders(path = "/guilds/$guildId/members/$userId/roles/$roleId")
                .retrieveWithCustomErrorHandler(errorHandler = ignoreNotFoundErrorHandler)

            logger.debug("Successfully added role $roleId to Discord guild member with ID: $userId")
        }
    }

    /**
     * Removes all roles from a guild member except the free entry role.
     *
     * @param userId The ID of the user to remove the role from
     */
    fun retainOnlyEntryRoleOfGuildMember(userId: String) {
        val request = DiscordPatchGuildMemberRolesRequest(
            roles = listOf(entryRoleId),
        )
        restClient
            .patch()
            .uriAndHeaders(path = "/guilds/$guildId/members/$userId")
            .jsonBody(request)
            .retrieveWithCustomErrorHandler(errorHandler = ignoreNotFoundErrorHandler)

        logger.debug("Successfully removed all premium member roles from Discord guild member with ID: $userId")
    }

    /**
     * Updates the roles of a guild member by setting the complete list of roles.
     * This method preserves roles that are not managed by our application.
     *
     * @param userId The ID of the user to update roles for
     * @param roles The complete list of role IDs to assign to the user
     */
    fun patchGuildMemberRoles(
        userId: String,
        roles: List<String>,
    ) {
        executeWithWaitForRateLimit {
            val request = DiscordPatchGuildMemberRolesRequest(
                roles = roles,
            )
            restClient
                .patch()
                .uriAndHeaders(path = "/guilds/$guildId/members/$userId")
                .jsonBody(request)
                .retrieveWithCustomErrorHandler(errorHandler = ignoreNotFoundErrorHandler)

            logger.debug("Successfully updated roles for Discord guild member with ID: $userId to: $roles")
        }
    }

    private val ignoreNotFoundErrorHandler = RestClient.ResponseSpec.ErrorHandler { request, response ->
        val body = BufferedReader(response.body.reader()).readLines().joinToString(separator = " ")
        logger.warn(
            """
            An error code occurred while calling ${request.method} ${request.uri}.
            Response [code: ${response.statusCode}, body: $body]
            """.trimIndent(),
        )

        if (response.statusCode != HttpStatus.NOT_FOUND) {
            throw IntegrationException(
                statusCode = response.statusCode.value(),
                responseBody = body,
                message = "Error ${response.statusCode} - ${request.method} ${request.uri}",
            )
        }
    }

    private fun <T> executeWithWaitForRateLimit(logic: () -> T): T = executeWithRetry(
        retryOnStatusCode = 429,
        retryLogic = {
            val waitTime = 5.seconds
            logger.info("Rate limit reached, waiting for ${waitTime}s ...")
            Thread.sleep(waitTime.inWholeMilliseconds)
        },
        logic = logic,
    )
}

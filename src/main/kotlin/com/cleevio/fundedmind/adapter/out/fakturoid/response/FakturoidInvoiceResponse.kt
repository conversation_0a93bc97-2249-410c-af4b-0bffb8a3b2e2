package com.cleevio.fundedmind.adapter.out.fakturoid.response

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Response model for Fakturoid invoice.
 * Based on https://www.fakturoid.cz/api/v3/#invoice
 */
data class FakturoidInvoiceResponse(
    val id: Long,

    @JsonProperty("custom_id")
    val customId: String?,

    @JsonProperty("subject_id")
    val subjectId: Long,

    @JsonProperty("number")
    val invoiceNumber: String,

    @JsonProperty("issued_on")
    val issuedOn: String,

    @JsonProperty("total")
    val total: String,

    @JsonProperty("html_url")
    val htmlUrl: String,

    @JsonProperty("url")
    val apiUrl: String,

    @JsonProperty("updated_at")
    val updatedAt: String,

    @JsonProperty("created_at")
    val createdAt: String,

    @JsonProperty("status")
    val status: String,

    val lines: List<FakturoidInvoiceLineResponse>,
)

/**
 * Response model for a line item in a Fakturoid invoice.
 */
data class FakturoidInvoiceLineResponse(
    val id: Long,
    val name: String,
    val quantity: String,

    @JsonProperty("unit_name")
    val unitName: String,

    @JsonProperty("unit_price")
    val unitPrice: String,

    @JsonProperty("vat_rate")
    val vatRate: String,
)

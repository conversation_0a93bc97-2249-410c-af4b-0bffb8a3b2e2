package com.cleevio.fundedmind.adapter.out.hubspot.connector

import com.cleevio.fundedmind.domain.common.constant.StudentTier

enum class HubspotTier {
    NO_TIER,
    BASECAMP,
    MASTERCLASS,
    EXCLUSIVE,
    ;

    companion object {
        fun fromStudentTier(studentTier: StudentTier): HubspotTier = when (studentTier) {
            StudentTier.NO_TIER -> NO_TIER
            StudentTier.BASECAMP -> BASECAMP
            StudentTier.MASTERCLASS -> MASTERCLASS
            StudentTier.EXCLUSIVE -> EXCLUSIVE
        }
    }
}

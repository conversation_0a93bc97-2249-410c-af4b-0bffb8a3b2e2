package com.cleevio.fundedmind.adapter.out.hubspot.connector.request

import com.fasterxml.jackson.annotation.JsonRawValue

data class HubspotContactRequest(
    val association: List<Association>,
    @JsonRawValue val properties: String,
) {

    data class Association(
        val types: List<Type>,
        val to: List<Id>,
    ) {
        companion object {
            fun ofEmpty() = Association(
                types = emptyList(),
                to = emptyList(),
            )
        }
    }

    data class Type(
        val category: String,
        val typeId: Int,
    )

    data class Id(
        val value: String,
    )
}

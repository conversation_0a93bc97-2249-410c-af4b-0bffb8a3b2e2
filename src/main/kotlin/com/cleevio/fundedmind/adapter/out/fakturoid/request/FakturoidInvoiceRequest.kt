package com.cleevio.fundedmind.adapter.out.fakturoid.request

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

/**
 * Request model for creating a Fakturoid invoice.
 * Based on https://www.fakturoid.cz/api/v3/#invoice
 */
data class FakturoidInvoiceRequest(
    @JsonProperty("custom_id")
    val customId: String? = null,

    @JsonProperty("subject_id")
    val subjectId: Long,

    @JsonProperty("issued_on")
    val issuedOn: String,

    @JsonProperty("show_already_paid_note_in_pdf")
    val showAlreadyPaidNoteInPdf: Boolean = false,

    val due: Int, // Number of days until the invoice becomes overdue

    val lines: List<FakturoidInvoiceLineRequest>,

    @JsonProperty("vat_price_mode")
    val vatPriceMode: String? = null, // "without_vat" or "from_total_with_vat"
)

/**
 * Request model for a line item in a Fakturoid invoice.
 */
data class FakturoidInvoiceLineRequest(
    val name: String,
    val quantity: String,

    @JsonProperty("unit_name")
    val unitName: String,

    @JsonProperty("unit_price")
    val unitPrice: Double,

    @JsonProperty("vat_rate")
    val vatRate: BigDecimal? = null,
)

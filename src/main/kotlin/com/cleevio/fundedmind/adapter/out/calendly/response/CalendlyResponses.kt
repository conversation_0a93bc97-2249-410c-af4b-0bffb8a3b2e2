package com.cleevio.fundedmind.adapter.out.calendly.response

import com.cleevio.fundedmind.application.common.type.CalendlyWebhookUri
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant

/**
 * See documentation:
 * https://developer.calendly.com/api-docs/faac832d7c57d-list-webhook-subscriptions
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class CalendlyListWebhooksResponse(
    @JsonProperty("collection")
    val collection: List<WebhookSubscription>,
) {

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class WebhookSubscription(
        @JsonProperty("uri")
        val uri: CalendlyWebhookUri,
        @JsonProperty("created_at")
        val createdAt: Instant,
        @JsonProperty("callback_url")
        val callbackUrl: String,
        @JsonProperty("events")
        val events: List<String>,
        @JsonProperty("state")
        val state: String, // active / disabled
    )
}

/**
 * see https://developer.calendly.com/api-docs/c1ddc06ce1f1b-create-webhook-subscription
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class CalendlyCreateWebhookResponse(
    @JsonProperty("resource")
    val resource: WebhookSubscription,
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class WebhookSubscription(
        @JsonProperty("uri")
        val uri: CalendlyWebhookUri,
        @JsonProperty("created_at")
        val createdAt: Instant,
        @JsonProperty("callback_url")
        val callbackUrl: String,
    )
}

/**
 * see https://developer.calendly.com/api-docs/eaed2e61a6bc3-list-organization-memberships
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class CalendlyListUsersResponse(
    @JsonProperty("collection")
    val collection: List<CalendlyOrganization>,
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class CalendlyOrganization(
        @JsonProperty("user")
        val user: CalendlyUser,
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class CalendlyUser(
        @JsonProperty("uri")
        val uri: String,
        @JsonProperty("email")
        val email: String,
        @JsonProperty("name")
        val name: String,
        @JsonProperty("scheduling_url")
        val schedulingUrl: String,
    )
}

/**
 * see https://developer.calendly.com/api-docs/afb2e9fe3a0a0-cancel-event
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class CalendlyCancelScheduledEventResponse(
    @JsonProperty("resource")
    val resource: CalendlyCancellation,
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class CalendlyCancellation(
        @JsonProperty("canceled_by")
        val canceledBy: String,
        @JsonProperty("reason")
        val reason: String,
    )
}

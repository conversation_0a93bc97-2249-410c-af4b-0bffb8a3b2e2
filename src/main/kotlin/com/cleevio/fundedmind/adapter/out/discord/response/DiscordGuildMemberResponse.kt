package com.cleevio.fundedmind.adapter.out.discord.response

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant

/**
 * Response object for Discord guild member API
 * See https://discord.com/developers/docs/resources/guild#guild-member-object
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class DiscordGuildMemberResponse(
    val user: DiscordUser,
    val roles: List<String>,
    @JsonProperty("joined_at")
    val joinedAt: Instant,
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class DiscordUser(
        val id: String,
        val username: String,
        @JsonProperty("global_name")
        val globalName: String?,
    )
}

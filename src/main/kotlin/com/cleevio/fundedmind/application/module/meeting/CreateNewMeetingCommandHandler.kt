package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.meeting.command.CreateNewMeetingCommand
import com.cleevio.fundedmind.application.module.meeting.event.MeetingCreatedEvent
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.fundedmind.domain.meeting.CreateMeetingService
import com.cleevio.fundedmind.domain.meeting.traderinmeeting.CreateTraderInMeetingService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateNewMeetingCommandHandler(
    private val traderFinderService: TraderFinderService,
    private val createMeetingService: CreateMeetingService,
    private val createTraderInMeetingService: CreateTraderInMeetingService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<IdResult, CreateNewMeetingCommand> {

    override val command = CreateNewMeetingCommand::class

    @Transactional
    @Lock(module = Locks.Meeting.MODULE, lockName = Locks.Meeting.CREATE)
    override fun handle(@LockFieldParameter("name") command: CreateNewMeetingCommand): IdResult {
        val createdMeeting = createMeetingService.create(
            name = command.name,
            color = command.color,
            startAt = command.startAt,
            finishAt = command.finishAt,
            description = command.description,
            invitedTiers = command.invitedTiers,
            invitedDiscordUsers = command.invitedDiscordUsers,
            meetingUrl = command.meetingUrl,
            recordingUrl = command.recordingUrl,
        ).also { createdMeeting ->
            command.traderIds.run {
                traderFinderService.checkIfAllExist(this)

                createTraderInMeetingService.create(
                    meetingId = createdMeeting.id,
                    traderIds = this,
                )
            }
        }

        applicationEventPublisher.publishEvent(MeetingCreatedEvent(meetingId = createdMeeting.id))

        return IdResult(createdMeeting.id)
    }
}

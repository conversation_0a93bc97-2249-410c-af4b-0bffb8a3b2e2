package com.cleevio.fundedmind.application.module.lesson.service

import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.lesson.event.LessonDeletedEvent
import com.cleevio.fundedmind.application.module.lesson.finder.LessonFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteLessonService(
    private val lessonFinderService: LessonFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {

    @Transactional
    @Lock(module = Locks.Lesson.MODULE, lockName = Locks.Lesson.UPDATE)
    fun deleteById(
        @LockArgumentParameter courseModuleId: UUID,
        lessonId: UUID,
    ) {
        lessonFinderService
            .getById(lessonId)
            .also { it.checkRelatedToModule(courseModuleId = courseModuleId) }
            .apply { softDelete() }
            .also { applicationEventPublisher.publishEvent(LessonDeletedEvent(lessonId = it.id)) }
    }

    @Transactional
    @Lock(module = Locks.Lesson.MODULE, lockName = Locks.Lesson.UPDATE)
    fun deleteAllInCourseModule(@LockArgumentParameter courseModuleId: UUID) {
        lessonFinderService
            .findAllNonDeletedByCourseModuleId(courseModuleId)
            .forEach { lesson ->
                lesson.softDelete()
                applicationEventPublisher.publishEvent(LessonDeletedEvent(lessonId = lesson.id))
            }
    }
}

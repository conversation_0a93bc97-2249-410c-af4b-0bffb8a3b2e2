package com.cleevio.fundedmind.application.module.lesson.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachment
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentRepository
import com.cleevio.fundedmind.domain.lesson.exception.LessonAttachmentNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class LessonAttachmentFinderService(
    private val lessonAttachmentRepository: LessonAttachmentRepository,
) : BaseFinderService<LessonAttachment>(lessonAttachmentRepository) {

    override fun errorBlock(message: String) = throw LessonAttachmentNotFoundException(message)

    override fun getEntityType() = LessonAttachment::class

    fun getAllByLessonId(lessonId: UUID): List<LessonAttachment> =
        lessonAttachmentRepository.findAllByLessonId(lessonId)

    fun findMaxDisplayOrderNonDeleted(lessonId: UUID): Int? =
        lessonAttachmentRepository.findMaxDisplayOrderNonDeleted(lessonId)
}

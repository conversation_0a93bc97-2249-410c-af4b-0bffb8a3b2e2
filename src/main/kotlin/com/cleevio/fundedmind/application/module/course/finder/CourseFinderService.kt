package com.cleevio.fundedmind.application.module.course.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.course.CourseRepository
import com.cleevio.fundedmind.domain.course.exception.CourseNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class CourseFinderService(
    private val courseRepository: CourseRepository,
) : BaseFinderService<Course>(courseRepository) {

    override fun errorBlock(message: String) = throw CourseNotFoundException(message)

    override fun getEntityType() = Course::class

    fun findMaxListingOrderNonDeleted(courseCategory: CourseCategory): Int? =
        courseRepository.findMaxListingOrderNonDeleted(courseCategory)

    fun countByCategoryNonDeleted(courseCategory: CourseCategory): Long =
        courseRepository.countByCourseCategoryAndDeletedAtIsNull(courseCategory)

    fun countPublishedByCategoryNonDeleted(courseCategory: CourseCategory): Long =
        courseRepository.countByCourseCategoryAndDeletedAtIsNullAndPublishedIsTrue(courseCategory)

    fun getByIdAndCourseCategoryNonDeleted(
        courseId: UUID,
        courseCategory: CourseCategory,
    ): Course = courseRepository.findByIdAndCourseCategoryAndDeletedAtIsNull(courseId, courseCategory)
        ?: errorBlock("Non-deleted Course: '$courseId' with category: '$courseCategory' not found.")

    fun findFirstPublishedByCategoryNonDeleted(category: CourseCategory): Course? =
        courseRepository.findFirstPublishedByCategoryNonDeleted(category)

    fun findAllNonDeletedInCategory(courseCategory: CourseCategory): List<Course> =
        courseRepository.findAllByCourseCategoryAndDeletedAtIsNull(courseCategory)

    fun getNonDeletedCourseByLessonId(lessonId: UUID): Course = courseRepository.findNonDeletedByLessonId(lessonId)
        ?: errorBlock("Non-deleted Course not found for Lesson: '$lessonId'.")

    fun getNonDeletedCourseByIdAndModuleIdAndLessonId(
        courseId: UUID,
        courseModuleId: UUID,
        lessonId: UUID,
    ): Course = courseRepository.findNonDeletedCourseByIdAndModuleIdAndLessonId(
        courseId = courseId,
        courseModuleId = courseModuleId,
        lessonId = lessonId,
    ) ?: errorBlock(
        "Non-deleted Course not found for combination " +
            "courseId: '$courseId', courseModuleId: '$courseModuleId', lessonId: '$lessonId'.",
    )

    fun getNonDeletedCourseByIdAndModuleId(
        courseId: UUID,
        courseModuleId: UUID,
    ): Course = courseRepository.findNonDeletedCourseByIdAndModuleId(
        courseId = courseId,
        courseModuleId = courseModuleId,
    ) ?: errorBlock(
        "Non-deleted Course not found for combination " +
            "courseId: '$courseId', courseModuleId: '$courseModuleId'.",
    )

    fun getNonDeletedCourseModuleId(courseModuleId: UUID): Course =
        courseRepository.findNonDeletedCourseByModuleId(courseModuleId = courseModuleId)
            ?: errorBlock("Non-deleted Course not found for courseModuleId: '$courseModuleId'.")
}

package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.coursemodule.finder.CourseModuleFinderService
import com.cleevio.fundedmind.application.module.lesson.port.out.ListLessonsInCourseModulePort
import com.cleevio.fundedmind.application.module.lesson.query.ListLessonsInCourseModuleQuery
import org.springframework.stereotype.Component

@Component
class ListLessonsInCourseModuleQueryHandler(
    val courseModuleFinderService: CourseModuleFinderService,
    val listLessonsInCourseModulePort: ListLessonsInCourseModulePort,
) : Query<PERSON><PERSON><PERSON><ListLessonsInCourseModuleQuery.Result, ListLessonsInCourseModuleQuery> {

    override val query = ListLessonsInCourseModuleQuery::class

    override fun handle(query: ListLessonsInCourseModuleQuery): ListLessonsInCourseModuleQuery.Result {
        courseModuleFinderService
            .getById(query.filter.courseModuleId)
            .apply { checkRelatedToCourse(query.filter.courseId) }

        return listLessonsInCourseModulePort(filter = query.filter)
    }
}

package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.meeting.command.DeleteMeetingCommand
import com.cleevio.fundedmind.application.module.meeting.event.MeetingDeletedEvent
import com.cleevio.fundedmind.application.module.meeting.finder.MeetingFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DeleteMeetingCommandHandler(
    private val meetingFinderService: MeetingFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, DeleteMeetingCommand> {

    override val command = DeleteMeetingCommand::class

    @Transactional
    @Lock(module = Locks.Meeting.MODULE, lockName = Locks.Meeting.UPDATE)
    override fun handle(@LockFieldParameter("meetingId") command: DeleteMeetingCommand) {
        meetingFinderService
            .getById(command.meetingId)
            .apply { softDelete() }
            .also { applicationEventPublisher.publishEvent(MeetingDeletedEvent(meetingId = it.id)) }
    }
}

package com.cleevio.fundedmind.application.module.lesson.port.out

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.module.comment.query.GetLessonCommentQuery
import com.cleevio.fundedmind.application.module.comment.query.GetLessonCommentsQuery
import com.cleevio.fundedmind.application.module.lesson.query.GetModuleLessonPlaylistQuery
import com.cleevio.fundedmind.application.module.lesson.query.ListLessonsInCourseModuleQuery
import com.cleevio.fundedmind.application.module.lesson.query.WatchLessonQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import java.util.UUID

interface WatchLessonPort {
    operator fun invoke(
        userId: UUID,
        lessonId: UUID,
    ): WatchLessonQuery.Result
}

interface GetModuleLessonPlaylistPort {
    operator fun invoke(
        userId: UUID,
        courseId: UUID,
        courseModuleId: UUID,
    ): GetModuleLessonPlaylistQuery.Result
}

interface GetLessonCommentsPort {
    fun getForScrolling(
        lessonId: UUID,
        userId: UUID,
        gameLevelMapper: (GameLevel, LevelVisibility) -> GameLevel?,
        infiniteScroll: InfiniteScroll<UUID>,
    ): InfiniteScrollSlice<GetLessonCommentsQuery.Result, UUID>

    fun getByCommentId(
        commentId: UUID,
        userId: UUID,
        lessonId: UUID,
        gameLevelMapper: (GameLevel, LevelVisibility) -> GameLevel?,
    ): GetLessonCommentQuery.Result
}

interface ListLessonsInCourseModulePort {
    operator fun invoke(filter: ListLessonsInCourseModuleQuery.Filter): ListLessonsInCourseModuleQuery.Result
}

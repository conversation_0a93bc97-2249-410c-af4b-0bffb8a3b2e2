package com.cleevio.fundedmind.application.module.course.service

import com.cleevio.fundedmind.application.common.util.ifTrue
import com.cleevio.fundedmind.application.module.course.finder.CourseFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.course.exception.CourseAttachmentsNotAccessibleToStudentException
import com.cleevio.fundedmind.domain.course.exception.CourseIsLockedForUserException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CheckUserAccessToMaterialService(
    private val appUserFinderService: AppUserFinderService,
    private val studentFinderService: StudentFinderService,
    private val courseFinderService: CourseFinderService,
) {

    @Transactional(readOnly = true)
    fun checkUserAccessToAttachmentsOfCourse(
        userId: UUID,
        courseId: UUID,
    ) {
        courseFinderService
            .getById(courseId)
            .apply {
                isLockedFor(userId).ifTrue {
                    throw CourseAttachmentsNotAccessibleToStudentException(
                        "Course: '$id' is not accessible to user: '$userId'",
                    )
                }
            }
    }

    @Transactional(readOnly = true)
    fun checkUserAccessToLesson(
        userId: UUID,
        lessonId: UUID,
    ) {
        courseFinderService
            .getNonDeletedCourseByLessonId(lessonId = lessonId).apply {
                isLockedFor(userId).ifTrue {
                    throw CourseIsLockedForUserException("Lesson: $lessonId is not accessible to user: '$userId'")
                }
            }
    }

    @Transactional(readOnly = true)
    fun checkUserAccessToModule(
        userId: UUID,
        courseId: UUID,
        courseModuleId: UUID,
    ) {
        courseFinderService
            .getNonDeletedCourseByIdAndModuleId(
                courseId = courseId,
                courseModuleId = courseModuleId,
            ).apply {
                isLockedFor(userId).ifTrue {
                    throw CourseIsLockedForUserException("Module: $courseModuleId is not accessible to user: '$userId'")
                }
            }
    }

    @Transactional(readOnly = true)
    fun checkUserAccessToModule(
        userId: UUID,
        courseModuleId: UUID,
    ) {
        courseFinderService
            .getNonDeletedCourseModuleId(
                courseModuleId = courseModuleId,
            ).apply {
                isLockedFor(userId).ifTrue {
                    throw CourseIsLockedForUserException("Module: $courseModuleId is not accessible to user: '$userId'")
                }
            }
    }

    @Transactional(readOnly = true)
    fun checkUserAccessToLesson(
        userId: UUID,
        lessonId: UUID,
        courseId: UUID,
        courseModuleId: UUID,
    ) {
        courseFinderService
            .getNonDeletedCourseByIdAndModuleIdAndLessonId(
                courseId = courseId,
                courseModuleId = courseModuleId,
                lessonId = lessonId,
            )
            .apply {
                isLockedFor(userId).ifTrue {
                    throw CourseIsLockedForUserException("Lesson: $lessonId is not accessible to user: '$userId'")
                }
            }
    }

    private fun Course.isLockedFor(userId: UUID): Boolean {
        val user = appUserFinderService.getById(userId)

        if (!user.role.isStudent) {
            return false // trader and admin always have access
        }

        return studentFinderService
            .getById(userId)
            .isLockedForStudent(this.visibleToTiers, this.visibleToDiscordUsers)
    }
}

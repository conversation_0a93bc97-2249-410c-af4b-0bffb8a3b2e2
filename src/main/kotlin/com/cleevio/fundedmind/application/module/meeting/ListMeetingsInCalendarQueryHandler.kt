package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.common.util.ifTrue
import com.cleevio.fundedmind.application.common.util.toEndOfTheDay
import com.cleevio.fundedmind.application.common.util.toStartOfTheDay
import com.cleevio.fundedmind.application.module.meeting.finder.MeetingFinderService
import com.cleevio.fundedmind.application.module.meeting.query.ListMeetingsInCalendarQuery
import com.cleevio.fundedmind.application.module.meeting.query.ListMeetingsInCalendarQuery.MeetingInCalendarListing
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.Student
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class ListMeetingsInCalendarQueryHandler(
    private val appUserFinderService: AppUserFinderService,
    private val studentFinderService: StudentFinderService,
    private val meetingFinderService: MeetingFinderService,
) : QueryHandler<ListMeetingsInCalendarQuery.Result, ListMeetingsInCalendarQuery> {

    override val query = ListMeetingsInCalendarQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: ListMeetingsInCalendarQuery): ListMeetingsInCalendarQuery.Result {
        val user = appUserFinderService.getById(query.userId)

        val student: Student? = user.role.isStudent.ifTrue { studentFinderService.getById(query.userId) }

        val meetings = meetingFinderService.findNonDeletedMeetingsInRange(
            rangeStart = query.filter.startDate.toStartOfTheDay(),
            rangeEnd = query.filter.endDate.toEndOfTheDay(),
        )

        val models = meetings
            .map { meeting ->
                MeetingInCalendarListing(
                    meetingId = meeting.id,
                    name = meeting.name,
                    color = meeting.color,
                    startAt = meeting.startAt,
                    finishAt = meeting.finishAt,
                    isLockedForMe = when (user.role) {
                        UserRole.ADMIN -> false

                        UserRole.TRADER -> false

                        UserRole.STUDENT -> requireNotNull(student).isLockedForStudent(
                            allowedTiers = meeting.invitedTiers,
                            allowedDiscordUser = meeting.invitedDiscordUsers,
                        )
                    },
                )
            }
            .sortedBy { it.startAt }

        return ListMeetingsInCalendarQuery.Result(models)
    }
}

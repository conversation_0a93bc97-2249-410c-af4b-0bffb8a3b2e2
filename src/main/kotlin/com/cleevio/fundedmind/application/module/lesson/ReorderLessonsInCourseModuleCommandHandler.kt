package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.coursemodule.finder.CourseModuleFinderService
import com.cleevio.fundedmind.application.module.lesson.command.ReorderLessonsInCourseModuleCommand
import com.cleevio.fundedmind.application.module.lesson.finder.LessonFinderService
import com.cleevio.fundedmind.domain.lesson.exception.ActiveLessonsMismatchException
import com.cleevio.library.lockinghandler.service.LockService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class ReorderLessonsInCourseModuleCommandHandler(
    private val courseModuleFinderService: CourseModuleFinderService,
    private val lessonFinderService: LessonFinderService,
    private val lockService: LockService,
) : CommandHandler<Unit, ReorderLessonsInCourseModuleCommand> {

    override val command = ReorderLessonsInCourseModuleCommand::class

    @Transactional
    override fun handle(command: ReorderLessonsInCourseModuleCommand) {
        courseModuleFinderService
            .getById(command.courseModuleId)
            .apply { checkRelatedToCourse(command.courseId) }

        checkAllLessonsInCourseModuleAreProvided(command)

        lockService.obtainBlockingLock(
            module = Locks.Lesson.MODULE,
            lockName = Locks.Lesson.UPDATE,
            /* params = */
            command.courseModuleId.toString(),
        ).use {
            command.lessonOrderings.forEach { (lessonId, newListingOrder) ->
                lessonFinderService
                    .getByIdAndCourseModuleIdNonDeleted(lessonId = lessonId, courseModuleId = command.courseModuleId)
                    .updateListingOrder(newListingOrder)
            }
        }
    }

    private fun checkAllLessonsInCourseModuleAreProvided(command: ReorderLessonsInCourseModuleCommand) {
        val lessonsInModuleCount = lessonFinderService.countByCourseModuleIdNonDeleted(command.courseModuleId)
        if (lessonsInModuleCount != command.lessonOrderings.size.toLong()) {
            throw ActiveLessonsMismatchException(
                "Cannot reorder lessons because provided lessons do not reflect non-deleted lessons of given module.",
            )
        }
    }
}

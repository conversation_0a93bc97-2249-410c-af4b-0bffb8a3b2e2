package com.cleevio.fundedmind.application.module.lesson.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.lesson.Lesson
import com.cleevio.fundedmind.domain.lesson.LessonRepository
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class LessonFinderService(
    private val lessonRepository: LessonRepository,
) : BaseFinderService<Lesson>(lessonRepository) {

    override fun errorBlock(message: String) = throw LessonNotFoundException(message)

    override fun getEntityType() = Lesson::class

    fun findMaxListingOrderNonDeleted(courseModuleId: UUID): Int? =
        lessonRepository.findMaxListingOrderNonDeleted(courseModuleId)

    fun countByCourseModuleIdNonDeleted(courseModuleId: UUID): Long =
        lessonRepository.countByCourseModuleIdAndDeletedAtIsNull(courseModuleId = courseModuleId)

    fun getByIdAndCourseModuleIdNonDeleted(
        lessonId: UUID,
        courseModuleId: UUID,
    ): Lesson = lessonRepository.findByIdAndCourseModuleIdAndDeletedAtIsNull(
        id = lessonId,
        courseModuleId = courseModuleId,
    ) ?: errorBlock("Non-deleted Lesson: '$lessonId' in Module: '$courseModuleId' not found.")

    fun findAllNonDeletedByCourseModuleId(courseModuleId: UUID): List<Lesson> =
        lessonRepository.findAllByCourseModuleIdAndDeletedAtIsNull(courseModuleId = courseModuleId)

    fun findAllLessonIdsForOverview(courseCategory: CourseCategory): List<UUID> =
        lessonRepository.findAllLessonIdsForOverview(courseCategory)
}

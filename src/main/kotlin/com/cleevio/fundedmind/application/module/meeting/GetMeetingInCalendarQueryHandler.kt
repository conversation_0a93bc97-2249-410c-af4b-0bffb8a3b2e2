package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.common.util.ifFalse
import com.cleevio.fundedmind.application.common.util.ifTrue
import com.cleevio.fundedmind.application.module.file.finder.AppFileFinderService
import com.cleevio.fundedmind.application.module.meeting.finder.MeetingFinderService
import com.cleevio.fundedmind.application.module.meeting.finder.TraderInMeetingFinderService
import com.cleevio.fundedmind.application.module.meeting.query.GetMeetingInCalendarQuery
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.fundedmind.domain.meeting.Meeting
import com.cleevio.fundedmind.domain.meeting.traderinmeeting.TraderInMeeting
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.Student
import com.cleevio.fundedmind.domain.user.trader.Trader
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetMeetingInCalendarQueryHandler(
    private val appUserFinderService: AppUserFinderService,
    private val studentFinderService: StudentFinderService,
    private val meetingFinderService: MeetingFinderService,
    private val appFileFinderService: AppFileFinderService,
    private val traderInMeetingFinderService: TraderInMeetingFinderService,
    private val traderFinderService: TraderFinderService,
) : QueryHandler<GetMeetingInCalendarQuery.Result, GetMeetingInCalendarQuery> {

    override val query = GetMeetingInCalendarQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetMeetingInCalendarQuery): GetMeetingInCalendarQuery.Result {
        val user = appUserFinderService.getById(query.userId)

        val meeting = meetingFinderService.getById(query.meetingId)

        val student: Student? = user.role.isStudent.ifTrue { studentFinderService.getById(query.userId) }

        val isLockedForMe = when (user.role) {
            UserRole.ADMIN -> false

            UserRole.TRADER -> false

            UserRole.STUDENT -> requireNotNull(student).isLockedForStudent(
                allowedTiers = meeting.invitedTiers,
                allowedDiscordUser = meeting.invitedDiscordUsers,
            )
        }

        return GetMeetingInCalendarQuery.Result(
            meetingId = meeting.id,
            name = meeting.name,
            color = meeting.color,
            startAt = meeting.startAt,
            finishAt = meeting.finishAt,
            description = meeting.description,
            invitedTiers = meeting.invitedTiers,
            invitedDiscordUsers = meeting.invitedDiscordUsers,
            coverPhoto = meeting.coverPhotoFileId?.let { appFileFinderService.getImageById(it) },
            traders = getTraderBios(meeting),
            isLockedForMe = isLockedForMe,
            unlockedData = isLockedForMe.ifFalse {
                GetMeetingInCalendarQuery.MeetingUnlockedData(
                    meetingUrl = meeting.meetingUrl,
                    recordingUrl = meeting.recordingUrl,
                )
            },
        )
    }

    private fun getTraderBios(meeting: Meeting): List<GetMeetingInCalendarQuery.TraderInMeetingBio> {
        val tradersInMeeting: List<TraderInMeeting> = traderInMeetingFinderService.getAllByMeetingId(meeting.id)
        val traders: List<Trader> = tradersInMeeting
            .map { it.traderId }
            .toSet()
            .let { traderFinderService.getAllByIds(it) }

        val tradersProfilePictures = appFileFinderService.findAllImagesByIds(
            traders.mapNotNull {
                it.profilePictureFileId
            },
        )

        val traderBios = traders
            .map { trader ->
                GetMeetingInCalendarQuery.TraderInMeetingBio(
                    traderId = trader.id,
                    displayOrder = tradersInMeeting.first { it.traderId == trader.id }.displayOrder,
                    position = trader.position,
                    firstName = trader.firstName,
                    lastName = trader.lastName,
                    profilePicture = trader.profilePictureFileId?.let { fileId ->
                        tradersProfilePictures.find { profilePic -> profilePic.imageId == fileId }
                    },
                )
            }
            .sortedBy { it.displayOrder }

        return traderBios
    }
}

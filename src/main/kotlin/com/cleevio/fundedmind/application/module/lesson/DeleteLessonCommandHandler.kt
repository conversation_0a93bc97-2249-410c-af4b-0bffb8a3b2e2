package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.module.coursemodule.finder.CourseModuleFinderService
import com.cleevio.fundedmind.application.module.lesson.command.DeleteLessonCommand
import com.cleevio.fundedmind.application.module.lesson.service.DeleteLessonService
import org.springframework.stereotype.Component

@Component
class DeleteLessonCommandHandler(
    private val courseModuleFinderService: CourseModuleFinderService,
    private val deleteLessonService: DeleteLessonService,
) : CommandHandler<Unit, DeleteLessonCommand> {

    override val command = DeleteLessonCommand::class

    override fun handle(command: DeleteLessonCommand) {
        courseModuleFinderService
            .getById(command.courseModuleId)
            .apply { checkRelatedToCourse(command.courseId) }
            .also { module -> deleteLessonService.deleteById(module.id, command.lessonId) }
    }
}

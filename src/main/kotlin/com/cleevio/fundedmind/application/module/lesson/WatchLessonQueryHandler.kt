package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.course.service.CheckUserAccessToMaterialService
import com.cleevio.fundedmind.application.module.lesson.port.out.WatchLessonPort
import com.cleevio.fundedmind.application.module.lesson.query.WatchLessonQuery
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class WatchLessonQueryHandler(
    private val checkUserAccessToMaterialService: CheckUserAccessToMaterialService,
    private val watchLessonPort: WatchLessonPort,
) : QueryHandler<WatchLessonQuery.Result, WatchLessonQuery> {

    override val query = WatchLessonQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: WatchLessonQuery): WatchLessonQuery.Result {
        val watchLessonResult = watchLessonPort(userId = query.userId, lessonId = query.filter.lessonId)

        checkUserAccessToMaterialService.checkUserAccessToLesson(
            userId = query.userId,
            lessonId = watchLessonResult.lessonId,
        )

        return watchLessonResult
    }
}

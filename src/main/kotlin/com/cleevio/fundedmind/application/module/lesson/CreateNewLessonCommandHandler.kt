package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.coursemodule.finder.CourseModuleFinderService
import com.cleevio.fundedmind.application.module.lesson.command.CreateNewLessonCommand
import com.cleevio.fundedmind.application.module.lesson.event.LessonCreatedEvent
import com.cleevio.fundedmind.application.module.lesson.finder.LessonFinderService
import com.cleevio.fundedmind.domain.lesson.CreateLessonService
import com.cleevio.fundedmind.domain.lesson.attachment.CreateLessonAttachmentService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateNewLessonCommandHandler(
    private val createLessonService: CreateLessonService,
    private val createLessonAttachmentService: CreateLessonAttachmentService,
    private val courseModuleFinderService: CourseModuleFinderService,
    private val lessonFinderService: LessonFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<IdResult, CreateNewLessonCommand> {

    override val command = CreateNewLessonCommand::class

    @Transactional
    @Lock(module = Locks.Lesson.MODULE, lockName = Locks.Lesson.CREATE)
    override fun handle(@LockFieldParameter("courseModuleId") command: CreateNewLessonCommand): IdResult {
        courseModuleFinderService
            .getById(command.courseModuleId)
            .apply { checkRelatedToCourse(command.courseId) }

        val currentMaxOrder = lessonFinderService.findMaxListingOrderNonDeleted(command.courseModuleId) ?: 0

        val createdLesson = createLessonService.create(
            courseModuleId = command.courseModuleId,
            listingOrder = currentMaxOrder + 1,
            title = command.title,
            videoUrl = command.videoUrl,
            description = command.description,
            durationInSeconds = command.durationInSeconds,
            thumbnailUrl = command.thumbnailUrl,
            thumbnailAnimationUrl = command.thumbnailAnimationUrl,
        ).also { createdLesson ->
            createLessonAttachmentService.createAttachments(
                lessonId = createdLesson.id,
                attachments = command.attachments
                    .filter { it.forCreate }
                    .mapIndexed { idx, attachmentInput -> attachmentInput.toValue(idx + 1) },
            )
        }

        applicationEventPublisher.publishEvent(LessonCreatedEvent(lessonId = createdLesson.id))

        return IdResult(createdLesson.id)
    }
}

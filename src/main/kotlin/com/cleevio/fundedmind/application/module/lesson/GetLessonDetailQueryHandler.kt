package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.coursemodule.finder.CourseModuleFinderService
import com.cleevio.fundedmind.application.module.file.finder.AppFileFinderService
import com.cleevio.fundedmind.application.module.lesson.finder.LessonAttachmentFinderService
import com.cleevio.fundedmind.application.module.lesson.finder.LessonFinderService
import com.cleevio.fundedmind.application.module.lesson.query.GetLessonDetailQuery
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachment
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetLessonDetailQueryHandler(
    private val courseModuleFinderService: CourseModuleFinderService,
    private val lessonFinderService: LessonFinderService,
    private val lessonAttachmentFinderService: LessonAttachmentFinderService,
    private val appFileFinderService: AppFileFinderService,
) : QueryHandler<GetLessonDetailQuery.Result, GetLessonDetailQuery> {

    override val query = GetLessonDetailQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetLessonDetailQuery): GetLessonDetailQuery.Result {
        courseModuleFinderService
            .getById(query.courseModuleId)
            .apply { checkRelatedToCourse(query.courseId) }

        val lesson = lessonFinderService.getById(query.lessonId)

        return GetLessonDetailQuery.Result(
            lessonId = lesson.id,
            courseModuleId = lesson.courseModuleId,
            title = lesson.title,
            description = lesson.description,
            durationInSeconds = lesson.durationInSeconds,
            videoUrl = lesson.videoUrl,
            thumbnailUrl = lesson.thumbnailUrl,
            thumbnailAnimationUrl = lesson.thumbnailAnimationUrl,
            attachments = lessonAttachmentFinderService
                .getAllByLessonId(lesson.id)
                .map(::mapLessonAttachment)
                .sortedBy { it.displayOrder },
        )
    }

    private fun mapLessonAttachment(lessonAttachment: LessonAttachment) =
        GetLessonDetailQuery.GetLessonDetailAttachment(
            attachmentId = lessonAttachment.id,
            displayOrder = lessonAttachment.displayOrder,
            nameWithExtension = lessonAttachment.nameWithExtension,
            type = lessonAttachment.type,
            document = lessonAttachment.attachmentDocumentFileId?.let { fileId ->
                appFileFinderService.getDocumentById(fileId)
            },
        )
}

package com.cleevio.fundedmind.application.module.lesson.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import com.cleevio.fundedmind.application.common.validation.NullOrNotBlankAndLimited
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentValue
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import jakarta.validation.Valid
import jakarta.validation.constraints.PositiveOrZero
import java.util.UUID

data class CreateNewLessonCommand(
    val courseId: UUID,
    val courseModuleId: UUID,
    @field:NotBlankAndLimited val title: String,
    @field:NullOrNotBlankAndLimited val description: String?,
    @field:NotBlankAndLimited val videoUrl: String,
    @field:PositiveOrZero val durationInSeconds: Int,
    @field:NotBlankAndLimited val thumbnailUrl: String,
    @field:NotBlankAndLimited val thumbnailAnimationUrl: String,
    val attachments: List<@Valid LessonAttachmentInput>,
) : Command<IdResult>

data class UpdateLessonCommand(
    val courseId: UUID,
    val courseModuleId: UUID,
    val lessonId: UUID,
    @field:NotBlankAndLimited val title: String,
    @field:NullOrNotBlankAndLimited val description: String?,
    @field:NotBlankAndLimited val videoUrl: String,
    @field:PositiveOrZero val durationInSeconds: Int,
    @field:NotBlankAndLimited val thumbnailUrl: String,
    @field:NotBlankAndLimited val thumbnailAnimationUrl: String,
    val attachments: List<@Valid LessonAttachmentInput>,
) : Command<Unit>

data class DeleteLessonCommand(
    val courseId: UUID,
    val courseModuleId: UUID,
    val lessonId: UUID,
) : Command<Unit>

data class ReorderLessonsInCourseModuleCommand(
    val courseId: UUID,
    val courseModuleId: UUID,
    val lessonOrderings: List<@Valid LessonOrderingInput>,
) : Command<Unit> {
    data class LessonOrderingInput(
        val lessonId: UUID,
        @field:PositiveOrZero val newListingOrder: Int,
    )
}

data class LessonAttachmentInput(
    val lessonAttachmentId: UUID?,
    @field:NotBlankAndLimited val name: String,
    val type: LessonAttachmentType,
) {
    val forUpdate: Boolean
        get() = lessonAttachmentId != null

    val forCreate: Boolean
        get() = lessonAttachmentId == null

    fun toValue(displayOrder: Int) = LessonAttachmentValue(
        displayOrder = displayOrder,
        name = name,
        type = type,
    )
}

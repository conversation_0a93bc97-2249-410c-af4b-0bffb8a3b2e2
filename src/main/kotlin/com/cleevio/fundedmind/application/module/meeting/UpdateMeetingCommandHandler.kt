package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.meeting.command.UpdateMeetingCommand
import com.cleevio.fundedmind.application.module.meeting.event.MeetingUpdatedEvent
import com.cleevio.fundedmind.application.module.meeting.finder.MeetingFinderService
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.fundedmind.domain.meeting.Meeting
import com.cleevio.fundedmind.domain.meeting.traderinmeeting.CreateTraderInMeetingService
import com.cleevio.fundedmind.domain.meeting.traderinmeeting.DeleteTraderInMeetingService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateMeetingCommandHandler(
    private val meetingFinderService: MeetingFinderService,
    private val traderFinderService: TraderFinderService,
    private val deleteTraderInMeetingService: DeleteTraderInMeetingService,
    private val createTraderInMeetingService: CreateTraderInMeetingService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, UpdateMeetingCommand> {

    override val command = UpdateMeetingCommand::class

    @Transactional
    @Lock(module = Locks.Meeting.MODULE, lockName = Locks.Meeting.UPDATE)
    override fun handle(@LockFieldParameter("meetingId") command: UpdateMeetingCommand) {
        meetingFinderService
            .getById(command.meetingId)
            .also { meeting -> updateMeeting(meeting, command) }
            .also { meeting -> updateTradersInMeeting(meeting, command) }
            .also { applicationEventPublisher.publishEvent(MeetingUpdatedEvent(meetingId = it.id)) }
    }

    private fun updateMeeting(
        meeting: Meeting,
        command: UpdateMeetingCommand,
    ) {
        meeting.update(
            name = command.name,
            color = command.color,
            startAt = command.startAt,
            finishAt = command.finishAt,
            description = command.description,
            invitedTiers = command.invitedTiers,
            invitedDiscordUsers = command.invitedDiscordUsers,
            meetingUrl = command.meetingUrl,
            recordingUrl = command.recordingUrl,
        )
    }

    private fun updateTradersInMeeting(
        meeting: Meeting,
        command: UpdateMeetingCommand,
    ) {
        deleteTraderInMeetingService.deleteAllTradersFromMeeting(meeting.id)

        command.traderIds.run {
            traderFinderService.checkIfAllExist(this)
            createTraderInMeetingService.create(
                meetingId = meeting.id,
                traderIds = this,
            )
        }
    }
}

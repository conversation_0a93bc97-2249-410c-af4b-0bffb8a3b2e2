package com.cleevio.fundedmind.application.module.course.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.PositiveOrZero
import java.util.UUID

data class CreateNewCourseCommand(
    @field:NotBlankAndLimited val title: String,
    val courseCategory: CourseCategory,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    @field:NotBlankAndLimited val description: String,
    val traderId: UUID,
    val color: Color,
    @field:NotBlankAndLimited val thumbnailUrl: String,
    @field:NotBlankAndLimited val thumbnailAnimationUrl: String,
    @field:NotBlankAndLimited val trailerUrl: String,
    val public: Boolean,
    val homepage: <PERSON>olean,
) : Command<IdResult>

data class UpdateCourseCommand(
    val courseId: UUID,
    @field:NotBlankAndLimited val title: String,
    val courseCategory: CourseCategory,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    @field:NotBlankAndLimited val description: String,
    val traderId: UUID,
    val color: Color,
    @field:NotBlankAndLimited val thumbnailUrl: String,
    @field:NotBlankAndLimited val thumbnailAnimationUrl: String,
    @field:NotBlankAndLimited val trailerUrl: String,
    val public: Boolean,
    val homepage: Boolean,
) : Command<Unit>

data class DeleteCourseCommand(
    val courseId: UUID,
) : Command<Unit>

data class PublishCourseCommand(
    val courseId: UUID,
) : Command<Unit>

data class HideCourseCommand(
    val courseId: UUID,
) : Command<Unit>

data class ReorderCoursesByCategoryCommand(
    val courseOrderings: List<@Valid CourseOrderingInput>,
    val courseCategory: CourseCategory,
) : Command<Unit> {
    data class CourseOrderingInput(
        val courseId: UUID,
        @field:PositiveOrZero val newListingOrder: Int,
    )
}

data class UserDownloadsCourseAttachmentCommand(
    val userId: UUID,
    val courseId: UUID,
    val attachmentId: UUID,
) : Command<UserDownloadsCourseAttachmentCommand.Result> {

    @Schema(name = "UserDownloadsCourseAttachmentResult")
    data class Result(
        val documentId: UUID,
        val documentTemporaryUrl: String,
    )
}

data class CheckUserAccessToLessonCommand(
    val userId: UUID,
    val courseId: UUID,
    val courseModuleId: UUID,
    val lessonId: UUID,
) : Command<Unit>

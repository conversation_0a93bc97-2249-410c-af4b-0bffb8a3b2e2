package com.cleevio.fundedmind.application.module.gamepayoutsettings.query

import com.cleevio.fundedmind.application.common.query.Query
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.Year
import java.util.UUID

data class GetGamePayoutSettingsQuery(
    val year: Year,
) : Query<GetGamePayoutSettingsQuery.Result> {

    @Schema(name = "GetGamePayoutSettingsResult")
    data class Result(
        val id: UUID,
        val year: Int,
        val payoutOffset: BigDecimal,
    )
}

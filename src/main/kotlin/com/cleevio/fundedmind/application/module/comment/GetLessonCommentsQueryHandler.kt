package com.cleevio.fundedmind.application.module.comment

import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.comment.query.GetLessonCommentsQuery
import com.cleevio.fundedmind.application.module.lesson.port.out.GetLessonCommentsPort
import com.cleevio.fundedmind.application.module.user.student.service.GameLevelPrivacyMappingService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class GetLessonCommentsQueryHandler(
    private val getLessonCommentsPort: GetLessonCommentsPort,
    private val gameLevelPrivacyMappingService: GameLevelPrivacyMappingService,
) : QueryHandler<InfiniteScrollSlice<GetLessonCommentsQuery.Result, UUID>, GetLessonCommentsQuery> {

    override val query = GetLessonCommentsQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetLessonCommentsQuery): InfiniteScrollSlice<GetLessonCommentsQuery.Result, UUID> =
        getLessonCommentsPort.getForScrolling(
            lessonId = query.lessonId,
            userId = query.userId,
            gameLevelMapper = { level, visibility ->
                gameLevelPrivacyMappingService.gameLevelOrNull(
                    meUserId = query.userId,
                    studentGameLevel = level,
                    studentGameLevelVisibility = visibility,
                )
            },
            infiniteScroll = query.infiniteScroll,
        )
}

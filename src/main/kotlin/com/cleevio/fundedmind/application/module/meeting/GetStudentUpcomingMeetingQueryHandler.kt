package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.meeting.port.out.UpcomingMeetingPort
import com.cleevio.fundedmind.application.module.meeting.port.out.UpcomingMeetingPort.UpcomingMeeting
import com.cleevio.fundedmind.application.module.meeting.query.GetStudentUpcomingMeetingQuery
import com.cleevio.fundedmind.application.module.mentoringmeeting.port.out.UpcomingMentoringMeetingPort
import com.cleevio.fundedmind.application.module.mentoringmeeting.port.out.UpcomingMentoringMeetingPort.UpcomingMentoringMeeting
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetStudentUpcomingMeetingQueryHandler(
    private val studentFinderService: StudentFinderService,
    private val upcomingMeetingPort: UpcomingMeetingPort,
    private val upcomingMentoringMeetingPort: UpcomingMentoringMeetingPort,
) : Query<PERSON><PERSON><PERSON><GetStudentUpcomingMeetingQuery.Result, GetStudentUpcomingMeetingQuery> {

    override val query = GetStudentUpcomingMeetingQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetStudentUpcomingMeetingQuery): GetStudentUpcomingMeetingQuery.Result {
        val student = studentFinderService.getById(query.studentId)

        // Find the closest upcoming regular meeting
        val meeting = upcomingMeetingPort
            .findAllByFinishAfter(query.now)
            .filter { meeting ->
                when {
                    student.studentTier in meeting.invitedTiers -> true
                    meeting.invitedDiscordUsers && student.discordSubscription -> true
                    else -> false
                }
            }
            .minByOrNull { it.startAt }
            ?.let(::mapUpcomingMeeting)

        // Find the closest upcoming mentoring meeting
        val mentoringMeeting = upcomingMentoringMeetingPort
            .findAllByStudentIdAndFinishAtAfter(query.studentId, query.now)
            .minByOrNull { it.startAt }
            ?.let(::mapUpcomingMentoringMeeting)

        // Determine which meeting is coming up sooner
        val soonestUpcomingMeeting = listOfNotNull(meeting, mentoringMeeting).minByOrNull { it.startAt }

        return GetStudentUpcomingMeetingQuery.Result(soonestUpcomingMeeting)
    }

    private fun mapUpcomingMeeting(upcomingMeeting: UpcomingMeeting) =
        GetStudentUpcomingMeetingQuery.StudentUpcomingMeeting(
            upcomingMeetingId = upcomingMeeting.id,
            name = upcomingMeeting.name,
            color = upcomingMeeting.color,
            startAt = upcomingMeeting.startAt,
            finishAt = upcomingMeeting.finishAt,
            coverPhoto = upcomingMeeting.coverPhoto,
            isMentoring = false,
        )

    private fun mapUpcomingMentoringMeeting(upcomingMeeting: UpcomingMentoringMeeting) =
        GetStudentUpcomingMeetingQuery.StudentUpcomingMeeting(
            upcomingMeetingId = upcomingMeeting.id,
            name = "1:1 Mentoring",
            color = upcomingMeeting.color,
            startAt = upcomingMeeting.startAt,
            finishAt = upcomingMeeting.finishAt,
            coverPhoto = upcomingMeeting.coverPhoto,
            isMentoring = true,
        )
}

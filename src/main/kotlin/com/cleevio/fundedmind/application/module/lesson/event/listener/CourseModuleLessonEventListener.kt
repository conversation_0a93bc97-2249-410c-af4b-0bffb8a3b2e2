package com.cleevio.fundedmind.application.module.lesson.event.listener

import com.cleevio.fundedmind.application.module.coursemodule.event.CourseModuleDeletedEvent
import com.cleevio.fundedmind.application.module.lesson.service.DeleteLessonService
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class CourseModuleLessonEventListener(
    private val deleteLessonService: DeleteLessonService,
) {

    @EventListener
    fun handleCourseModuleDeletedEvent(event: CourseModuleDeletedEvent) {
        deleteLessonService.deleteAllInCourseModule(event.courseModuleId)
    }
}

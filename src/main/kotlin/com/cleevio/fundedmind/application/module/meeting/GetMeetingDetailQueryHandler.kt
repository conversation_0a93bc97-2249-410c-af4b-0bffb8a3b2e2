package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.meeting.port.out.GetMeetingDetailPort
import com.cleevio.fundedmind.application.module.meeting.query.GetMeetingDetailQuery
import org.springframework.stereotype.Component

@Component
class GetMeetingDetailQueryHandler(
    private val getMeetingDetailPort: GetMeetingDetailPort,
) : QueryHandler<GetMeetingDetailQuery.Result, GetMeetingDetailQuery> {

    override val query = GetMeetingDetailQuery::class

    override fun handle(query: GetMeetingDetailQuery): GetMeetingDetailQuery.Result =
        getMeetingDetailPort(query.meetingId)
}

package com.cleevio.fundedmind.application.module.lesson.query

import com.cleevio.fundedmind.application.common.command.DocumentResult
import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.AutocompleteFilter
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.util.UUID

data class GetLessonDetailQuery(
    val courseId: UUID,
    val courseModuleId: UUID,
    val lessonId: UUID,
) : Query<GetLessonDetailQuery.Result> {

    @Schema(name = "GetLessonDetailResult")
    data class Result(
        val lessonId: UUID,
        val courseModuleId: UUID,
        val title: String,
        val videoUrl: String,
        val description: String?,
        val durationInSeconds: Int,
        val thumbnailUrl: String,
        val thumbnailAnimationUrl: String,
        val attachments: List<GetLessonDetailAttachment>,
    )

    @Schema(name = "GetLessonDetailAttachment")
    data class GetLessonDetailAttachment(
        val attachmentId: UUID,
        val displayOrder: Int,
        val nameWithExtension: String,
        val type: LessonAttachmentType,
        val document: DocumentResult?,
    )
}

data class ListLessonsInCourseModuleQuery(
    @field:Valid val filter: Filter,
) : Query<ListLessonsInCourseModuleQuery.Result> {

    data class Filter(
        override val searchString: String?,
        val courseId: UUID,
        val courseModuleId: UUID,
    ) : AutocompleteFilter

    @Schema(name = "ListLessonsInCourseModuleResult")
    data class Result(
        val data: List<LessonListing>,
    )

    @Schema(name = "ListLessonsInCourseModuleLesson")
    data class LessonListing(
        val lessonId: UUID,
        val listingOrder: Int,
        val videoUrl: String,
        val title: String,
        val durationInSeconds: Int,
        val attachmentCount: Int,
        val thumbnailUrl: String,
        val thumbnailAnimationUrl: String,
    )
}

data class WatchLessonQuery(
    val userId: UUID,
    @field:Valid val filter: Filter,
) : Query<WatchLessonQuery.Result> {

    data class Filter(
        val lessonId: UUID,
    )

    @Schema(name = "WatchLessonResult")
    data class Result(
        val lessonId: UUID,
        val title: String,
        val videoUrl: String,
        val description: String?,
        val durationInSeconds: Int,
        val thumbnailUrl: String,
        val thumbnailAnimationUrl: String,

        val traderBio: TraderBio,
        val progress: LessonProgress?,
        val attachments: List<AttachmentListing>,
        val commentCount: Int,

        val courseModuleId: UUID,
        val courseModuleTitle: String,
        val courseId: UUID,
        val courseCategory: CourseCategory,
        val courseTitle: String,
    )

    @Schema(name = "WatchLessonTraderBio")
    data class TraderBio(
        val traderId: UUID,
        val position: String,
        val firstName: String,
        val lastName: String,
        val profilePicture: ImageResult?,
        val badgeColor: BadgeColor,
    )

    @Schema(name = "WatchLessonProgress")
    data class LessonProgress(
        val seconds: Int,
        val finished: Boolean,
    )

    @Schema(name = "WatchLessonAttachment")
    data class AttachmentListing(
        val attachmentId: UUID,
        val displayOrder: Int,
        val nameWithExtension: String,
        val type: LessonAttachmentType,
        val document: DocumentResult?,
    )
}

data class GetModuleLessonPlaylistQuery(
    val userId: UUID,
    val courseId: UUID,
    val courseModuleId: UUID,
) : Query<GetModuleLessonPlaylistQuery.Result> {

    @Schema(name = "GetModuleLessonPlaylistResult")
    data class Result(
        val moduleId: UUID,
        val moduleTitle: String,
        val moduleDescription: String,
        val moduleShortDescription: String,
        val courseColor: Color,
        val lessons: List<PlaylistLesson>,
    ) {
        val lessonCount: Int = lessons.size
        val totalDurationInSeconds: Int = lessons.sumOf { it.durationInSeconds }
    }

    @Schema(name = "GetModuleLessonPlaylistLesson")
    data class PlaylistLesson(
        val lessonId: UUID,
        val listingOrder: Int,
        val thumbnailUrl: String,
        val thumbnailAnimationUrl: String,
        val title: String,
        val finished: Boolean,
        val durationInSeconds: Int,
        val watchedSeconds: Int,
    )
}

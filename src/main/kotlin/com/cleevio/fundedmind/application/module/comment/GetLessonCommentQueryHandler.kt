package com.cleevio.fundedmind.application.module.comment

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.comment.query.GetLessonCommentQuery
import com.cleevio.fundedmind.application.module.lesson.port.out.GetLessonCommentsPort
import com.cleevio.fundedmind.application.module.user.student.service.GameLevelPrivacyMappingService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetLessonCommentQueryHandler(
    private val getLessonCommentsPort: GetLessonCommentsPort,
    private val gameLevelPrivacyMappingService: GameLevelPrivacyMappingService,
) : QueryHandler<GetLessonCommentQuery.Result, GetLessonCommentQuery> {

    override val query = GetLessonCommentQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetLessonCommentQuery): GetLessonCommentQuery.Result =
        getLessonCommentsPort.getByCommentId(
            lessonId = query.lessonId,
            userId = query.userId,
            commentId = query.commentId,
            gameLevelMapper = { level, visibility ->
                gameLevelPrivacyMappingService.gameLevelOrNull(
                    meUserId = query.userId,
                    studentGameLevel = level,
                    studentGameLevelVisibility = visibility,
                )
            },
        )
}

package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.course.service.CheckUserAccessToMaterialService
import com.cleevio.fundedmind.application.module.lesson.port.out.GetModuleLessonPlaylistPort
import com.cleevio.fundedmind.application.module.lesson.query.GetModuleLessonPlaylistQuery
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetModuleLessonPlaylistQueryHandler(
    private val checkUserAccessToMaterialService: CheckUserAccessToMaterialService,
    private val getModuleLessonPlaylistPort: GetModuleLessonPlaylistPort,
) : QueryHandler<GetModuleLessonPlaylistQuery.Result, GetModuleLessonPlaylistQuery> {

    override val query = GetModuleLessonPlaylistQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetModuleLessonPlaylistQuery): GetModuleLessonPlaylistQuery.Result {
        checkUserAccessToMaterialService.checkUserAccessToModule(
            userId = query.userId,
            courseId = query.courseId,
            courseModuleId = query.courseModuleId,
        )

        return getModuleLessonPlaylistPort(
            userId = query.userId,
            courseId = query.courseId,
            courseModuleId = query.courseModuleId,
        )
    }
}

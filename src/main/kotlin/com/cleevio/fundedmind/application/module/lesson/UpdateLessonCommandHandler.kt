package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.coursemodule.finder.CourseModuleFinderService
import com.cleevio.fundedmind.application.module.file.service.AppFileService
import com.cleevio.fundedmind.application.module.lesson.command.UpdateLessonCommand
import com.cleevio.fundedmind.application.module.lesson.event.LessonUpdatedEvent
import com.cleevio.fundedmind.application.module.lesson.finder.LessonAttachmentFinderService
import com.cleevio.fundedmind.application.module.lesson.finder.LessonFinderService
import com.cleevio.fundedmind.domain.lesson.Lesson
import com.cleevio.fundedmind.domain.lesson.attachment.CreateLessonAttachmentService
import com.cleevio.fundedmind.domain.lesson.attachment.DeleteLessonAttachmentService
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachment
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateLessonCommandHandler(
    private val createLessonAttachmentService: CreateLessonAttachmentService,
    private val courseModuleFinderService: CourseModuleFinderService,
    private val lessonFinderService: LessonFinderService,
    private val deleteLessonAttachmentService: DeleteLessonAttachmentService,
    private val lessonAttachmentFinderService: LessonAttachmentFinderService,
    private val appFileService: AppFileService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, UpdateLessonCommand> {

    override val command = UpdateLessonCommand::class

    @Transactional
    @Lock(module = Locks.Lesson.MODULE, lockName = Locks.Lesson.UPDATE)
    override fun handle(@LockFieldParameter("courseModuleId") command: UpdateLessonCommand) {
        courseModuleFinderService
            .getById(command.courseModuleId)
            .apply { checkRelatedToCourse(courseId = command.courseId) }

        lessonFinderService
            .getById(command.lessonId)
            .also { it.checkRelatedToModule(courseModuleId = command.courseModuleId) }
            .also { lesson -> updateLesson(lesson, command) }
            .also { lesson -> changeLessonAttachments(lesson, command) }
            .also { applicationEventPublisher.publishEvent(LessonUpdatedEvent(lessonId = command.lessonId)) }
    }

    private fun changeLessonAttachments(
        lesson: Lesson,
        command: UpdateLessonCommand,
    ) {
        val currentAttachments = lessonAttachmentFinderService.getAllByLessonId(lesson.id)

        // update existing attachments
        command.attachments
            .filter { it.forUpdate }
            .forEach { attachmentInput ->
                currentAttachments
                    .find { it.id == attachmentInput.lessonAttachmentId }
                    ?.update(
                        nameWithExtension = attachmentInput.name,
                        type = attachmentInput.type,
                    )
            }

        // delete obsolete attachments
        currentAttachments
            .filter {
                // existing attachment that is not to be updated should be deleted
                it.id !in command.attachments
                    .filter { it.forUpdate }
                    .mapNotNull { it.lessonAttachmentId }
            }
            .forEach { obsoleteAttachment -> deleteObsoleteLessonAttachment(obsoleteAttachment) }

        val maxDisplayOrder = lessonAttachmentFinderService.findMaxDisplayOrderNonDeleted(lesson.id) ?: 0

        // create new attachments
        createLessonAttachmentService.createAttachments(
            lessonId = lesson.id,
            attachments = command.attachments
                .filter { it.forCreate }
                .map { it.toValue(displayOrder = maxDisplayOrder + 1) },
        )

        // fix display order so it always begins at 1
        lessonAttachmentFinderService
            .getAllByLessonId(lesson.id)
            .sortedBy { it.displayOrder }
            .forEachIndexed { idx, attachment -> attachment.updateDisplayOrder(newOrder = idx + 1) }
    }

    private fun deleteObsoleteLessonAttachment(lessonAttachment: LessonAttachment) {
        val fileIdToDelete = lessonAttachmentFinderService
            .getById(lessonAttachment.id)
            .attachmentDocumentFileId

        deleteLessonAttachmentService.deleteById(lessonAttachment.id)

        // FIXME: nemazat subory synchronne, ale robit scheduled cleanup external storagu
        fileIdToDelete?.let { appFileService.delete(it) }
    }

    private fun updateLesson(
        lesson: Lesson,
        command: UpdateLessonCommand,
    ) {
        lesson.update(
            title = command.title,
            description = command.description,
            videoUrl = command.videoUrl,
            durationInSeconds = command.durationInSeconds,
            thumbnailUrl = command.thumbnailUrl,
            thumbnailAnimationUrl = command.thumbnailAnimationUrl,
        )
    }
}

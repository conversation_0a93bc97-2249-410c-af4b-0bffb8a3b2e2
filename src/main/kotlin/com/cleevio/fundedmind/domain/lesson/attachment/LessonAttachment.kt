package com.cleevio.fundedmind.domain.lesson.attachment

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.DomainEntity
import com.cleevio.fundedmind.domain.file.constant.File
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON>
 */
@Table(name = "lesson_attachment")
@Entity
@DynamicUpdate
class LessonAttachment private constructor(
    id: UUID,
    val lessonId: UUID,
    displayOrder: Int,
    nameWithExtension: String,
    type: LessonAttachmentType,
    attachmentDocumentFileId: UUID?,
) : DomainEntity(id) {

    var displayOrder: Int = displayOrder
        private set

    var nameWithExtension: String = nameWithExtension
        private set

    @Enumerated(EnumType.STRING)
    var type: LessonAttachmentType = type
        private set

    @File(type = FileType.LESSON_ATTACHMENT)
    var attachmentDocumentFileId: UUID? = attachmentDocumentFileId
        private set

    companion object {
        fun addAttachmentToLesson(
            id: UUID = UUIDv7.randomUUID(),
            lessonId: UUID,
            displayOrder: Int,
            nameWithExtension: String,
            type: LessonAttachmentType,
        ) = LessonAttachment(
            id = id,
            lessonId = lessonId,
            displayOrder = displayOrder,
            nameWithExtension = nameWithExtension,
            type = type,
            attachmentDocumentFileId = null,
        ).also {
            require(displayOrder >= 1)
        }
    }

    fun changeAttachmentDocument(fileId: UUID?) {
        this.attachmentDocumentFileId = fileId
    }

    fun update(
        nameWithExtension: String,
        type: LessonAttachmentType,
    ) {
        this.nameWithExtension = nameWithExtension
        this.type = type
    }

    fun updateDisplayOrder(newOrder: Int) {
        require(newOrder >= 1)
        this.displayOrder = newOrder
    }
}

@Repository
interface LessonAttachmentRepository : JpaRepository<LessonAttachment, UUID> {
    @Query(
        """
        SELECT MAX(la.displayOrder) 
        FROM LessonAttachment la 
        WHERE la.lessonId = :lessonId
    """,
    )
    fun findMaxDisplayOrderNonDeleted(lessonId: UUID): Int?

    fun findAllByLessonId(lessonId: UUID): List<LessonAttachment>
}

package com.cleevio.fundedmind.domain.lesson.attachment

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteLessonAttachmentService(private val lessonAttachmentRepository: LessonAttachmentRepository) {

    @Transactional
    fun deleteById(lessonAttachmentId: UUID) {
        lessonAttachmentRepository.deleteById(lessonAttachmentId)
    }
}

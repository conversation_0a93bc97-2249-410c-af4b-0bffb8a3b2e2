package com.cleevio.fundedmind.domain.lesson

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.SoftDeletableEntity
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotRelatedToModuleException
import com.cleevio.fundedmind.domain.lesson.exception.LessonOrderCannotBeNegativeException
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Lekce v modulu
 */
@Table(name = "lesson")
@Entity
@DynamicUpdate
class Lesson private constructor(
    id: UUID,
    val courseModuleId: UUID,
    listingOrder: Int,
    title: String,
    videoUrl: String,
    description: String?,
    durationInSeconds: Int,
    thumbnailUrl: String,
    thumbnailAnimationUrl: String,
) : SoftDeletableEntity(id) {

    init {
        checkListingOrderIsPositiveOrZero(listingOrder)
    }

    var listingOrder: Int = listingOrder
        private set
    var title: String = title
        private set
    var videoUrl: String = videoUrl
        private set
    var description: String? = description
        private set
    var durationInSeconds: Int = durationInSeconds
        private set
    var thumbnailUrl: String = thumbnailUrl
        private set
    var thumbnailAnimationUrl: String = thumbnailAnimationUrl
        private set

    companion object {
        fun addNewLessonToModule(
            id: UUID = UUIDv7.randomUUID(),
            courseModuleId: UUID,
            listingOrder: Int,
            title: String,
            videoUrl: String,
            description: String?,
            durationInSeconds: Int,
            thumbnailUrl: String,
            thumbnailAnimationUrl: String,
        ) = Lesson(
            id = id,
            courseModuleId = courseModuleId,
            listingOrder = listingOrder,
            title = title,
            videoUrl = videoUrl,
            description = description,
            durationInSeconds = durationInSeconds,
            thumbnailUrl = thumbnailUrl,
            thumbnailAnimationUrl = thumbnailAnimationUrl,
        )
    }

    fun updateListingOrder(newOrder: Int) {
        checkListingOrderIsPositiveOrZero(newOrder)
        this.listingOrder = newOrder
    }

    fun update(
        title: String,
        videoUrl: String,
        description: String?,
        durationInSeconds: Int,
        thumbnailUrl: String,
        thumbnailAnimationUrl: String,
    ) {
        this.title = title
        this.description = description
        this.videoUrl = videoUrl
        this.durationInSeconds = durationInSeconds
        this.thumbnailUrl = thumbnailUrl
        this.thumbnailAnimationUrl = thumbnailAnimationUrl
    }

    private fun checkListingOrderIsPositiveOrZero(listingOrder: Int) {
        if (listingOrder >= 0) return

        throw LessonOrderCannotBeNegativeException("Listing order cannot be negative: '$listingOrder'")
    }

    fun checkRelatedToModule(courseModuleId: UUID) {
        if (this.courseModuleId != courseModuleId) {
            throw LessonNotRelatedToModuleException("Lesson: '$id' is not related to module: '$courseModuleId'")
        }
    }
}

@Repository
interface LessonRepository : JpaRepository<Lesson, UUID> {
    @Query(
        """
        SELECT MAX(l.listingOrder) 
        FROM Lesson l 
        WHERE l.courseModuleId = :courseModuleId AND l.deletedAt IS NULL
    """,
    )
    fun findMaxListingOrderNonDeleted(courseModuleId: UUID): Int?

    fun countByCourseModuleIdAndDeletedAtIsNull(courseModuleId: UUID): Long

    fun findByIdAndCourseModuleIdAndDeletedAtIsNull(
        id: UUID,
        courseModuleId: UUID,
    ): Lesson?

    fun findAllByCourseModuleIdAndDeletedAtIsNull(courseModuleId: UUID): List<Lesson>

    @Query(
        """
        SELECT l.id 
        FROM Lesson l
        INNER JOIN CourseModule m ON m.id = l.courseModuleId
        INNER JOIN Course c ON c.id = m.courseId
        WHERE l.deletedAt IS NULL
        AND m.deletedAt IS NULL
        AND c.deletedAt IS NULL
        AND c.published = true
        AND c.courseCategory = :courseCategory
    """,
    )
    fun findAllLessonIdsForOverview(courseCategory: CourseCategory): List<UUID>
}

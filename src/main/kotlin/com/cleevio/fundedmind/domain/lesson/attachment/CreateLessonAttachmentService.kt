package com.cleevio.fundedmind.domain.lesson.attachment

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateLessonAttachmentService(
    private val lessonAttachmentRepository: LessonAttachmentRepository,
) {

    @Transactional
    fun createAttachments(
        lessonId: UUID,
        attachments: List<LessonAttachmentValue>,
    ): Unit = attachments.forEach { attachment ->
        lessonAttachmentRepository.save(
            LessonAttachment.addAttachmentToLesson(
                lessonId = lessonId,
                displayOrder = attachment.displayOrder,
                nameWithExtension = attachment.name,
                type = attachment.type,
            ),
        )
    }
}

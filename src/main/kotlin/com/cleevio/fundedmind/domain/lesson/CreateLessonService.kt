package com.cleevio.fundedmind.domain.lesson

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateLessonService(
    private val lessonRepository: LessonRepository,
) {

    @Transactional
    fun create(
        courseModuleId: UUID,
        title: String,
        listingOrder: Int,
        videoUrl: String,
        description: String?,
        durationInSeconds: Int,
        thumbnailUrl: String,
        thumbnailAnimationUrl: String,
    ): Lesson = lessonRepository.save(
        Lesson.addNewLessonToModule(
            courseModuleId = courseModuleId,
            listingOrder = listingOrder,
            title = title,
            videoUrl = videoUrl,
            description = description,
            durationInSeconds = durationInSeconds,
            thumbnailUrl = thumbnailUrl,
            thumbnailAnimationUrl = thumbnailAnimationUrl,
        ),
    )
}

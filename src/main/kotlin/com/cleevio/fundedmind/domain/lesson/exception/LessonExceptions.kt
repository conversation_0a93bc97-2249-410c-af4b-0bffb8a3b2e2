package com.cleevio.fundedmind.domain.lesson.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class LessonNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.LESSON_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class LessonAttachmentNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.LESSON_ATTACHMENT_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class LessonAttachmentDocumentNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.LESSON_ATTACHMENT_DOCUMENT_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class LessonOrderCannotBeNegativeException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.LESSON_ORDER_CANNOT_BE_NEGATIVE,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ActiveLessonsMismatchException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.ACTIVE_LESSONS_MISMATCH,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class LessonNotRelatedToModuleException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.LESSON_NOT_RELATED_TO_MODULE,
    message = message,
)

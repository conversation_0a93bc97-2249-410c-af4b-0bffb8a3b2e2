package com.cleevio.fundedmind.domain.common.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class StudentTierMustBeInvitedException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_TIER_MUST_BE_INVITED,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class DiscordUsersMustBeInvitedException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.DISCORD_USERS_MUST_BE_INVITED,
    message = message,
)

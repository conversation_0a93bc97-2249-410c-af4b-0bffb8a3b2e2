package com.cleevio.fundedmind.domain.common.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.GONE)
class EntityIsDeletedException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.ENTITY_IS_DELETED,
    message = message,
)

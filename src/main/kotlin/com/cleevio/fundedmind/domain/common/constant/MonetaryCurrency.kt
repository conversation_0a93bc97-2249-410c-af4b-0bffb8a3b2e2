package com.cleevio.fundedmind.domain.common.constant

import java.math.BigDecimal
import java.math.RoundingMode

enum class MonetaryCurrency(val isoCode: String) {
    CZK("CZK"),
    ;

    fun toAmount(unitAmount: Long): BigDecimal = when (this) {
        CZK -> unitAmount.toBigDecimal()
            .divide(BigDecimal(100))
            .setScale(2, RoundingMode.HALF_UP)
    }

    companion object {
        fun fromIsoCode(isoCode: String): MonetaryCurrency = entries
            .find { enumEntry -> enumEntry.isoCode.lowercase() == isoCode.lowercase() }
            ?: throw IllegalArgumentException("Unsupported currency code: $isoCode")
    }
}

enum class TaxBehaviour {
    INCLUSIVE, // tax is part of the price
    EXCLUSIVE, // tax is separate
    ;

    companion object {
        fun fromStripe(stripeValue: String?): TaxBehaviour = when (stripeValue?.lowercase()) {
            "inclusive" -> INCLUSIVE
            "exclusive" -> EXCLUSIVE
            "unspecified", null -> INCLUSIVE
            else -> error("Unsupported tax behaviour: $stripeValue")
        }
    }
}

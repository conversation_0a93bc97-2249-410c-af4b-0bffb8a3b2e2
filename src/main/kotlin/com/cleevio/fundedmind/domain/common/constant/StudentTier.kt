package com.cleevio.fundedmind.domain.common.constant

enum class StudentTier(private val order: Int) {
    NO_TIER(0),
    BASECAMP(1),
    MASTERCLASS(2),
    EXCLUSIVE(3),
    ;

    fun isBelow(other: StudentTier): Boolean = this.order < other.order

    fun hasAccessTo(category: CourseCategory): Boolean = when (this) {
        NO_TIER -> false

        // basecamp tier has only basecamp content unlocked
        BASECAMP -> when (category) {
            CourseCategory.BASECAMP -> true
            CourseCategory.TRADING_BASICS -> false
            CourseCategory.STRATEGY -> false
            CourseCategory.RECORDING -> true
            CourseCategory.ADD_ON -> false
            CourseCategory.EXCLUSIVE -> false
        }

        // masterclass tier has unlocked all content except exclusive
        MASTERCLASS -> when (category) {
            CourseCategory.BASECAMP -> true
            CourseCategory.TRADING_BASICS -> true
            CourseCategory.STRATEGY -> true
            CourseCategory.RECORDING -> true
            CourseCategory.ADD_ON -> true
            CourseCategory.EXCLUSIVE -> false
        }

        // exclusive tier has unlocked all content
        EXCLUSIVE -> when (category) {
            CourseCategory.BASECAMP -> true
            CourseCategory.TRADING_BASICS -> true
            CourseCategory.STRATEGY -> true
            CourseCategory.RECORDING -> true
            CourseCategory.ADD_ON -> true
            CourseCategory.EXCLUSIVE -> true
        }
    }

    fun toEmailText(): String = when (this) {
        NO_TIER -> "No tier"
        BASECAMP -> "Basecamp"
        MASTERCLASS -> "Masterclass"
        EXCLUSIVE -> "Exclusive"
    }

    companion object {
        fun fromString(value: String?): StudentTier = when (value?.lowercase()) {
            "no_tier" -> NO_TIER
            "basecamp" -> BASECAMP
            "masterclass" -> MASTERCLASS
            "exclusive" -> EXCLUSIVE
            else -> error("Unknown student tier: $value")
        }
    }
}

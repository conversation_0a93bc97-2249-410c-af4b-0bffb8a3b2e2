package com.cleevio.fundedmind.domain.common.constant

enum class TraderMentoringAvailability {
    AUTOMATIC,
    PAUSED,
}

enum class TraderMentoring {
    NO,
    YES,
    PAUSED,
    ;

    companion object {
        fun determine(
            availability: TraderMentoringAvailability,
            traderHasSaleableProduct: <PERSON>olean,
        ): TraderMentoring = when {
            availability == TraderMentoringAvailability.PAUSED -> PAUSED
            traderHasSaleableProduct -> YES
            else -> NO
        }
    }
}

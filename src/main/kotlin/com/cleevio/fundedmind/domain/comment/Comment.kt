package com.cleevio.fundedmind.domain.comment

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.SoftDeletableEntity
import com.cleevio.fundedmind.domain.comment.exception.CommentOwnerIsWrongException
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Table(name = "comment")
@DynamicUpdate
@Entity
class Comment private constructor(
    id: UUID,
    val appUserId: UUID,
    text: String,
    val lessonId: UUID,
    val threadId: UUID?,
) : SoftDeletableEntity(id) {

    var text: String = text
        private set

    companion object {
        fun createNewComment(
            id: UUID = UUIDv7.randomUUID(),
            appUserId: UUID,
            text: String,
            lessonId: UUID,
            threadId: UUID?,
        ) = Comment(
            id = id,
            appUserId = appUserId,
            text = text,
            lessonId = lessonId,
            threadId = threadId,
        )
    }

    fun ownerUpdate(
        appUserId: UUID,
        text: String,
    ) {
        checkIfOwner(appUserId)
        this.text = text
    }

    fun ownerSoftDelete(appUserId: UUID) {
        checkIfOwner(appUserId)
        this.softDelete()
    }

    private fun checkIfOwner(appUserId: UUID) {
        if (this.appUserId != appUserId) {
            throw CommentOwnerIsWrongException("User: '$appUserId' is not the owner of the comment: '$id'.")
        }
    }
}

@Repository
interface CommentRepository : JpaRepository<Comment, UUID> {
    @Query(
        """
        SELECT DISTINCT c.appUserId
        FROM Comment c
        WHERE (c.id = :threadId OR c.threadId = :threadId)
        AND c.deletedAt IS NULL
    """,
    )
    fun findDistinctUsersInThreadByThreadId(threadId: UUID): Set<UUID>
}

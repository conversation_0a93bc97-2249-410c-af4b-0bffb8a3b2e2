package com.cleevio.fundedmind.domain.comment

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteCommentLikeService(
    private val commentLikeRepository: CommentLikeRepository,
) {

    @Transactional
    fun deleteByCommentIdAndAppUserId(
        commentId: UUID,
        appUserId: UUID,
    ) {
        commentLikeRepository.deleteAllByCommentIdAndAppUserId(commentId, appUserId)
    }

    @Transactional
    fun deleteAllByCommentId(commentId: UUID) {
        commentLikeRepository.deleteAllByCommentId(commentId)
    }
}

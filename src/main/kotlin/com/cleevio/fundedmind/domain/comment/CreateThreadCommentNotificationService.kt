package com.cleevio.fundedmind.domain.comment

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Service
class CreateThreadCommentNotificationService(
    private val threadCommentNotificationRepository: ThreadCommentNotificationRepository,
) {

    @Transactional
    fun create(
        appUserId: UUID,
        threadId: UUID,
        lastNotifiedAt: Instant,
    ): ThreadCommentNotification = threadCommentNotificationRepository.save(
        ThreadCommentNotification.createNew(
            appUserId = appUserId,
            threadId = threadId,
            lastNotifiedAt = lastNotifiedAt,
        ),
    )
}

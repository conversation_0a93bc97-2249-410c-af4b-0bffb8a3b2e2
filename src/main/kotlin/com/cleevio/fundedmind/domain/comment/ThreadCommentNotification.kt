package com.cleevio.fundedmind.domain.comment

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.DomainEntity
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

@Table(name = "thread_comment_notification")
@DynamicUpdate
@Entity
class ThreadCommentNotification private constructor(
    id: UUID,
    val appUserId: UUID,
    val threadId: UUID, // Comment.id
    val lastNotifiedAt: Instant,
) : DomainEntity(id) {

    companion object {
        fun createNew(
            id: UUID = UUIDv7.randomUUID(),
            appUserId: UUID,
            threadId: UUID,
            lastNotifiedAt: Instant,
        ) = ThreadCommentNotification(
            id = id,
            appUserId = appUserId,
            threadId = threadId,
            lastNotifiedAt = lastNotifiedAt,
        )
    }

    // No update methods - we'll delete and create new entries instead
}

@Repository
interface ThreadCommentNotificationRepository : JpaRepository<ThreadCommentNotification, UUID> {
    @Query(
        """
        SELECT tcn
        FROM ThreadCommentNotification tcn
        WHERE tcn.appUserId = :appUserId
        AND tcn.threadId = :threadId
        AND tcn.lastNotifiedAt > :since
        """,
    )
    fun findRecentNotification(
        appUserId: UUID,
        threadId: UUID,
        since: Instant,
    ): ThreadCommentNotification?

    @Modifying
    @Query("DELETE FROM ThreadCommentNotification WHERE appUserId = :appUserId AND threadId = :threadId")
    fun deleteByAppUserIdAndThreadId(
        appUserId: UUID,
        threadId: UUID,
    )
}

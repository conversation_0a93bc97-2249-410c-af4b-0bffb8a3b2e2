package com.cleevio.fundedmind.domain.comment

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateCommentLikeService(
    private val commentLikeRepository: CommentLikeRepository,
) {

    @Transactional
    fun create(
        commentId: UUID,
        appUserId: UUID,
    ): CommentLike {
        val commentLike = CommentLike.likeComment(
            commentId = commentId,
            appUserId = appUserId,
        )
        return commentLikeRepository.save(commentLike)
    }
}

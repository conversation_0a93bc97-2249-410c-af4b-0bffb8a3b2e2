package com.cleevio.fundedmind.domain.comment

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateCommentService(
    private val commentRepository: CommentRepository,
) {

    @Transactional
    fun create(
        appUserId: UUID,
        lessonId: UUID,
        text: String,
        threadId: UUID?,
    ): Comment {
        val comment = Comment.createNewComment(
            appUserId = appUserId,
            text = text,
            threadId = threadId,
            lessonId = lessonId,
        )
        return commentRepository.save(comment)
    }
}

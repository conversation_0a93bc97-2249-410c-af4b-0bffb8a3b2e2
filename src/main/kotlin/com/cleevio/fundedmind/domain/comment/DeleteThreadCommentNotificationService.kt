package com.cleevio.fundedmind.domain.comment

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteThreadCommentNotificationService(
    private val threadCommentNotificationRepository: ThreadCommentNotificationRepository,
) {

    @Transactional
    fun deleteByAppUserIdAndThreadId(
        appUserId: UUID,
        threadId: UUID,
    ) {
        threadCommentNotificationRepository.deleteByAppUserIdAndThreadId(
            appUserId = appUserId,
            threadId = threadId,
        )
    }
}

package com.cleevio.fundedmind.domain.comment

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.UpdatableEntity
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Table(name = "comment_like")
@DynamicUpdate
@Entity
class CommentLike private constructor(
    id: UUID,
    val commentId: UUID,
    val appUserId: UUID,
) : UpdatableEntity(id) {

    companion object {
        fun likeComment(
            id: UUID = UUIDv7.randomUUID(),
            commentId: UUID,
            appUserId: UUID,
        ) = CommentLike(
            id = id,
            commentId = commentId,
            appUserId = appUserId,
        )
    }
}

@Repository
interface CommentLikeRepository : JpaRepository<CommentLike, UUID> {
    fun findByCommentIdAndAppUserId(
        commentId: UUID,
        appUserId: UUID,
    ): CommentLike?

    fun findAllByCommentId(commentId: UUID): List<CommentLike>

    @Modifying
    @Query("DELETE FROM CommentLike WHERE commentId = :commentId AND appUserId = :appUserId")
    fun deleteAllByCommentIdAndAppUserId(
        commentId: UUID,
        appUserId: UUID,
    )

    @Modifying
    @Query("DELETE FROM CommentLike WHERE commentId = :commentId")
    fun deleteAllByCommentId(commentId: UUID)
}

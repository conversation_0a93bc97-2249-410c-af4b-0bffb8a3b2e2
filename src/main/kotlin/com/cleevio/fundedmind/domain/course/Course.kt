package com.cleevio.fundedmind.domain.course

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.application.common.util.replaceContents
import com.cleevio.fundedmind.domain.SoftDeletableEntity
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.exception.CourseHomepageRequiresPublicException
import com.cleevio.fundedmind.domain.course.exception.CourseMissingPictureException
import com.cleevio.fundedmind.domain.course.exception.CourseOrderCannotBeNegativeException
import com.cleevio.fundedmind.domain.file.constant.File
import com.cleevio.fundedmind.domain.file.constant.FileType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Kurz
 */
@Table(name = "course")
@Entity
@DynamicUpdate
class Course private constructor(
    id: UUID,
    listingOrder: Int,
    published: Boolean,
    title: String,
    courseCategory: CourseCategory,
    visibleToTiers: List<StudentTier>,
    visibleToDiscordUsers: Boolean,
    description: String,
    traderId: UUID,
    color: Color,
    thumbnailUrl: String,
    thumbnailAnimationUrl: String,
    trailerUrl: String,
    introPictureDesktopFileId: UUID?,
    introPictureMobileFileId: UUID?,
    public: Boolean,
    homepage: Boolean,
) : SoftDeletableEntity(id) {

    init {
        checkListingOrderIsPositiveOrZero(listingOrder)
        checkIfOnHomepageThenPublic(public = public, homepage = homepage)
    }

    var listingOrder: Int = listingOrder
        private set

    var published: Boolean = published
        private set

    var title: String = title
        private set

    @Enumerated(EnumType.STRING)
    var courseCategory: CourseCategory = courseCategory
        private set

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "text[]")
    private val _visibleToTiers: MutableList<StudentTier> = visibleToTiers.toMutableList()
    val visibleToTiers: List<StudentTier>
        get() = _visibleToTiers.toList()

    var visibleToDiscordUsers: Boolean = visibleToDiscordUsers
        private set

    var description: String = description
        private set

    var traderId: UUID = traderId
        private set

    @Enumerated(EnumType.STRING)
    var color: Color = color
        private set

    var thumbnailUrl: String = thumbnailUrl
        private set

    var thumbnailAnimationUrl: String = thumbnailAnimationUrl
        private set

    var trailerUrl: String = trailerUrl
        private set

    @File(type = FileType.COURSE_DESKTOP_INTRO_PHOTO)
    var introPictureDesktopFileId: UUID? = introPictureDesktopFileId
        private set

    @File(type = FileType.COURSE_MOBILE_INTRO_PHOTO)
    var introPictureMobileFileId: UUID? = introPictureMobileFileId
        private set

    var public: Boolean = public
        private set

    var homepage: Boolean = homepage
        private set

    companion object {
        fun newCourse(
            id: UUID = UUIDv7.randomUUID(),
            listingOrder: Int,
            title: String,
            courseCategory: CourseCategory,
            visibleToTiers: List<StudentTier>,
            visibleToDiscordUsers: Boolean,
            description: String,
            traderId: UUID,
            color: Color,
            thumbnailUrl: String,
            thumbnailAnimationUrl: String,
            trailerUrl: String,
            public: Boolean,
            homepage: Boolean,
        ) = Course(
            id = id,
            listingOrder = listingOrder,
            published = false,
            title = title,
            courseCategory = courseCategory,
            visibleToTiers = visibleToTiers,
            visibleToDiscordUsers = visibleToDiscordUsers,
            description = description,
            traderId = traderId,
            color = color,
            thumbnailUrl = thumbnailUrl,
            thumbnailAnimationUrl = thumbnailAnimationUrl,
            trailerUrl = trailerUrl,
            introPictureDesktopFileId = null,
            introPictureMobileFileId = null,
            public = public,
            homepage = homepage,
        )
    }

    fun changeIntroPictureDesktop(fileId: UUID?) {
        this.introPictureDesktopFileId = fileId
    }

    fun changeIntroPictureMobile(fileId: UUID?) {
        this.introPictureMobileFileId = fileId
    }

    fun updateListingOrder(newOrder: Int) {
        checkListingOrderIsPositiveOrZero(newOrder)
        this.listingOrder = newOrder
    }

    fun update(
        title: String,
        courseCategory: CourseCategory,
        visibleToTiers: List<StudentTier>,
        visibleToDiscordUsers: Boolean,
        description: String,
        traderId: UUID,
        color: Color,
        thumbnailUrl: String,
        thumbnailAnimationUrl: String,
        trailerUrl: String,
        public: Boolean,
        homepage: Boolean,
    ) {
        checkIfOnHomepageThenPublic(public = public, homepage = homepage)

        this.title = title
        this.courseCategory = courseCategory
        this._visibleToTiers.replaceContents(visibleToTiers)
        this.visibleToDiscordUsers = visibleToDiscordUsers
        this.description = description
        this.traderId = traderId
        this.color = color
        this.thumbnailUrl = thumbnailUrl
        this.thumbnailAnimationUrl = thumbnailAnimationUrl
        this.trailerUrl = trailerUrl
        this.public = public
        this.homepage = homepage
    }

    fun hide() {
        this.published = false
    }

    fun publish() {
        checkImagesPresent()
        this.published = true
    }

    private fun checkImagesPresent() {
        if (introPictureDesktopFileId == null) {
            throw CourseMissingPictureException("Course: '$id' does not have DESKTOP picture")
        }
        if (introPictureMobileFileId == null) {
            throw CourseMissingPictureException("Course: '$id' does not have MOBILE picture")
        }
    }

    private fun checkListingOrderIsPositiveOrZero(listingOrder: Int) {
        if (listingOrder >= 0) return

        throw CourseOrderCannotBeNegativeException("Listing order cannot be negative: '$listingOrder'")
    }

    private fun checkIfOnHomepageThenPublic(
        public: Boolean,
        homepage: Boolean,
    ) {
        if (homepage && !public) {
            throw CourseHomepageRequiresPublicException("Course: '$id' must be public if it is on a homepage.")
        }
    }
}

@Repository
interface CourseRepository : JpaRepository<Course, UUID> {
    @Query(
        """
        SELECT MAX(c.listingOrder) 
        FROM Course c 
        WHERE c.courseCategory = :courseCategory AND c.deletedAt IS NULL
    """,
    )
    fun findMaxListingOrderNonDeleted(courseCategory: CourseCategory): Int?

    fun countByCourseCategoryAndDeletedAtIsNull(courseCategory: CourseCategory): Long

    fun findByIdAndCourseCategoryAndDeletedAtIsNull(
        id: UUID,
        courseCategory: CourseCategory,
    ): Course?

    @Query(
        """
        SELECT c FROM Course c
        WHERE c.courseCategory = :category AND c.deletedAt IS NULL
        AND c.published = true
        ORDER BY c.listingOrder ASC
        LIMIT 1
    """,
    )
    fun findFirstPublishedByCategoryNonDeleted(category: CourseCategory): Course?

    fun countByCourseCategoryAndDeletedAtIsNullAndPublishedIsTrue(category: CourseCategory): Long

    fun findAllByCourseCategoryAndDeletedAtIsNull(category: CourseCategory): List<Course>

    @Query(
        """
        SELECT c 
        FROM Lesson l
        INNER JOIN CourseModule m ON m.id = l.courseModuleId
        INNER JOIN Course c ON c.id = m.courseId
        WHERE l.id = :lessonId
        AND l.deletedAt IS NULL
        AND m.deletedAt IS NULL
        AND c.deletedAt IS NULL
        """,
    )
    fun findNonDeletedByLessonId(lessonId: UUID): Course?

    @Query(
        """
        SELECT c 
        FROM Lesson l
        INNER JOIN CourseModule m ON m.id = l.courseModuleId
        INNER JOIN Course c ON c.id = m.courseId
        WHERE l.id = :lessonId AND l.deletedAt IS NULL
        AND m.id = :courseModuleId AND m.deletedAt IS NULL 
        AND c.id = :courseId AND c.deletedAt IS NULL
        """,
    )
    fun findNonDeletedCourseByIdAndModuleIdAndLessonId(
        courseId: UUID,
        courseModuleId: UUID,
        lessonId: UUID,
    ): Course?

    @Query(
        """
        SELECT c 
        FROM CourseModule m
        INNER JOIN Course c ON c.id = m.courseId
        AND m.id = :courseModuleId AND m.deletedAt IS NULL 
        AND c.id = :courseId AND c.deletedAt IS NULL
        """,
    )
    fun findNonDeletedCourseByIdAndModuleId(
        courseId: UUID,
        courseModuleId: UUID,
    ): Course?

    @Query(
        """
        SELECT c 
        FROM CourseModule m
        INNER JOIN Course c ON c.id = m.courseId
        AND m.id = :courseModuleId AND m.deletedAt IS NULL 
        AND c.deletedAt IS NULL
        """,
    )
    fun findNonDeletedCourseByModuleId(courseModuleId: UUID): Course?
}

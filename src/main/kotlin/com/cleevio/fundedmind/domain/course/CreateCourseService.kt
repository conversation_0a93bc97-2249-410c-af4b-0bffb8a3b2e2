package com.cleevio.fundedmind.domain.course

import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateCourseService(
    private val courseRepository: CourseRepository,
) {

    @Transactional
    fun create(
        title: String,
        listingOrder: Int,
        courseCategory: CourseCategory,
        visibleToTiers: List<StudentTier>,
        visibleToDiscordUsers: Boolean,
        description: String,
        traderId: UUID,
        color: Color,
        thumbnailUrl: String,
        thumbnailAnimationUrl: String,
        trailerUrl: String,
        public: Boolean,
        homepage: Boolean,
    ): Course = courseRepository.save(
        Course.newCourse(
            title = title,
            listingOrder = listingOrder,
            courseCategory = courseCategory,
            visibleToTiers = visibleToTiers,
            visibleToDiscordUsers = visibleToDiscordUsers,
            description = description,
            traderId = traderId,
            color = color,
            thumbnailUrl = thumbnailUrl,
            thumbnailAnimationUrl = thumbnailAnimationUrl,
            trailerUrl = trailerUrl,
            public = public,
            homepage = homepage,
        ),
    )
}
